/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["app/analytics/page"],{

/***/ "(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ArrowTrendingDownIcon.js":
/*!*******************************************************************************!*\
  !*** ./node_modules/@heroicons/react/24/outline/esm/ArrowTrendingDownIcon.js ***!
  \*******************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n\nfunction ArrowTrendingDownIcon(param, svgRef) {\n    let { title, titleId, ...props } = param;\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"svg\", Object.assign({\n        xmlns: \"http://www.w3.org/2000/svg\",\n        fill: \"none\",\n        viewBox: \"0 0 24 24\",\n        strokeWidth: 1.5,\n        stroke: \"currentColor\",\n        \"aria-hidden\": \"true\",\n        \"data-slot\": \"icon\",\n        ref: svgRef,\n        \"aria-labelledby\": titleId\n    }, props), title ? /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"title\", {\n        id: titleId\n    }, title) : null, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"path\", {\n        strokeLinecap: \"round\",\n        strokeLinejoin: \"round\",\n        d: \"M2.25 6 9 12.75l4.286-4.286a11.948 11.948 0 0 1 4.306 6.43l.776 2.898m0 0 3.182-5.511m-3.182 5.51-5.511-3.181\"\n    }));\n}\n_c = ArrowTrendingDownIcon;\nconst ForwardRef = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(ArrowTrendingDownIcon);\n_c1 = ForwardRef;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ForwardRef);\nvar _c, _c1;\n$RefreshReg$(_c, \"ArrowTrendingDownIcon\");\n$RefreshReg$(_c1, \"ForwardRef\");\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ArrowTrendingDownIcon.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ArrowTrendingUpIcon.js":
/*!*****************************************************************************!*\
  !*** ./node_modules/@heroicons/react/24/outline/esm/ArrowTrendingUpIcon.js ***!
  \*****************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n\nfunction ArrowTrendingUpIcon(param, svgRef) {\n    let { title, titleId, ...props } = param;\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"svg\", Object.assign({\n        xmlns: \"http://www.w3.org/2000/svg\",\n        fill: \"none\",\n        viewBox: \"0 0 24 24\",\n        strokeWidth: 1.5,\n        stroke: \"currentColor\",\n        \"aria-hidden\": \"true\",\n        \"data-slot\": \"icon\",\n        ref: svgRef,\n        \"aria-labelledby\": titleId\n    }, props), title ? /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"title\", {\n        id: titleId\n    }, title) : null, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"path\", {\n        strokeLinecap: \"round\",\n        strokeLinejoin: \"round\",\n        d: \"M2.25 18 9 11.25l4.306 4.306a11.95 11.95 0 0 1 5.814-5.518l2.74-1.22m0 0-5.94-2.281m5.94 2.28-2.28 5.941\"\n    }));\n}\n_c = ArrowTrendingUpIcon;\nconst ForwardRef = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(ArrowTrendingUpIcon);\n_c1 = ForwardRef;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ForwardRef);\nvar _c, _c1;\n$RefreshReg$(_c, \"ArrowTrendingUpIcon\");\n$RefreshReg$(_c1, \"ForwardRef\");\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ArrowTrendingUpIcon.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/BellIcon.js":
/*!******************************************************************!*\
  !*** ./node_modules/@heroicons/react/24/outline/esm/BellIcon.js ***!
  \******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n\nfunction BellIcon(param, svgRef) {\n    let { title, titleId, ...props } = param;\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"svg\", Object.assign({\n        xmlns: \"http://www.w3.org/2000/svg\",\n        fill: \"none\",\n        viewBox: \"0 0 24 24\",\n        strokeWidth: 1.5,\n        stroke: \"currentColor\",\n        \"aria-hidden\": \"true\",\n        \"data-slot\": \"icon\",\n        ref: svgRef,\n        \"aria-labelledby\": titleId\n    }, props), title ? /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"title\", {\n        id: titleId\n    }, title) : null, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"path\", {\n        strokeLinecap: \"round\",\n        strokeLinejoin: \"round\",\n        d: \"M14.857 17.082a23.848 23.848 0 0 0 5.454-1.31A8.967 8.967 0 0 1 18 9.75V9A6 6 0 0 0 6 9v.75a8.967 8.967 0 0 1-2.312 6.022c1.733.64 3.56 1.085 5.455 1.31m5.714 0a24.255 24.255 0 0 1-5.714 0m5.714 0a3 3 0 1 1-5.714 0\"\n    }));\n}\n_c = BellIcon;\nconst ForwardRef = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(BellIcon);\n_c1 = ForwardRef;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ForwardRef);\nvar _c, _c1;\n$RefreshReg$(_c, \"BellIcon\");\n$RefreshReg$(_c1, \"ForwardRef\");\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/BellIcon.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/BoltIcon.js":
/*!******************************************************************!*\
  !*** ./node_modules/@heroicons/react/24/outline/esm/BoltIcon.js ***!
  \******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n\nfunction BoltIcon(param, svgRef) {\n    let { title, titleId, ...props } = param;\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"svg\", Object.assign({\n        xmlns: \"http://www.w3.org/2000/svg\",\n        fill: \"none\",\n        viewBox: \"0 0 24 24\",\n        strokeWidth: 1.5,\n        stroke: \"currentColor\",\n        \"aria-hidden\": \"true\",\n        \"data-slot\": \"icon\",\n        ref: svgRef,\n        \"aria-labelledby\": titleId\n    }, props), title ? /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"title\", {\n        id: titleId\n    }, title) : null, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"path\", {\n        strokeLinecap: \"round\",\n        strokeLinejoin: \"round\",\n        d: \"m3.75 13.5 10.5-11.25L12 10.5h8.25L9.75 21.75 12 13.5H3.75Z\"\n    }));\n}\n_c = BoltIcon;\nconst ForwardRef = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(BoltIcon);\n_c1 = ForwardRef;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ForwardRef);\nvar _c, _c1;\n$RefreshReg$(_c, \"BoltIcon\");\n$RefreshReg$(_c1, \"ForwardRef\");\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/BoltIcon.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CalendarIcon.js":
/*!**********************************************************************!*\
  !*** ./node_modules/@heroicons/react/24/outline/esm/CalendarIcon.js ***!
  \**********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n\nfunction CalendarIcon(param, svgRef) {\n    let { title, titleId, ...props } = param;\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"svg\", Object.assign({\n        xmlns: \"http://www.w3.org/2000/svg\",\n        fill: \"none\",\n        viewBox: \"0 0 24 24\",\n        strokeWidth: 1.5,\n        stroke: \"currentColor\",\n        \"aria-hidden\": \"true\",\n        \"data-slot\": \"icon\",\n        ref: svgRef,\n        \"aria-labelledby\": titleId\n    }, props), title ? /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"title\", {\n        id: titleId\n    }, title) : null, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"path\", {\n        strokeLinecap: \"round\",\n        strokeLinejoin: \"round\",\n        d: \"M6.75 3v2.25M17.25 3v2.25M3 18.75V7.5a2.25 2.25 0 0 1 2.25-2.25h13.5A2.25 2.25 0 0 1 21 7.5v11.25m-18 0A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75m-18 0v-7.5A2.25 2.25 0 0 1 5.25 9h13.5A2.25 2.25 0 0 1 21 11.25v7.5\"\n    }));\n}\n_c = CalendarIcon;\nconst ForwardRef = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(CalendarIcon);\n_c1 = ForwardRef;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ForwardRef);\nvar _c, _c1;\n$RefreshReg$(_c, \"CalendarIcon\");\n$RefreshReg$(_c1, \"ForwardRef\");\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CalendarIcon.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ChartBarIcon.js":
/*!**********************************************************************!*\
  !*** ./node_modules/@heroicons/react/24/outline/esm/ChartBarIcon.js ***!
  \**********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n\nfunction ChartBarIcon(param, svgRef) {\n    let { title, titleId, ...props } = param;\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"svg\", Object.assign({\n        xmlns: \"http://www.w3.org/2000/svg\",\n        fill: \"none\",\n        viewBox: \"0 0 24 24\",\n        strokeWidth: 1.5,\n        stroke: \"currentColor\",\n        \"aria-hidden\": \"true\",\n        \"data-slot\": \"icon\",\n        ref: svgRef,\n        \"aria-labelledby\": titleId\n    }, props), title ? /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"title\", {\n        id: titleId\n    }, title) : null, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"path\", {\n        strokeLinecap: \"round\",\n        strokeLinejoin: \"round\",\n        d: \"M3 13.125C3 12.504 3.504 12 4.125 12h2.25c.621 0 1.125.504 1.125 1.125v6.75C7.5 20.496 6.996 21 6.375 21h-2.25A1.125 1.125 0 0 1 3 19.875v-6.75ZM9.75 8.625c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125v11.25c0 .621-.504 1.125-1.125 1.125h-2.25a1.125 1.125 0 0 1-1.125-1.125V8.625ZM16.5 4.125c0-.621.504-1.125 1.125-1.125h2.25C20.496 3 21 3.504 21 4.125v15.75c0 .621-.504 1.125-1.125 1.125h-2.25a1.125 1.125 0 0 1-1.125-1.125V4.125Z\"\n    }));\n}\n_c = ChartBarIcon;\nconst ForwardRef = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(ChartBarIcon);\n_c1 = ForwardRef;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ForwardRef);\nvar _c, _c1;\n$RefreshReg$(_c, \"ChartBarIcon\");\n$RefreshReg$(_c1, \"ForwardRef\");\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ChartBarIcon.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ChartPieIcon.js":
/*!**********************************************************************!*\
  !*** ./node_modules/@heroicons/react/24/outline/esm/ChartPieIcon.js ***!
  \**********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n\nfunction ChartPieIcon(param, svgRef) {\n    let { title, titleId, ...props } = param;\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"svg\", Object.assign({\n        xmlns: \"http://www.w3.org/2000/svg\",\n        fill: \"none\",\n        viewBox: \"0 0 24 24\",\n        strokeWidth: 1.5,\n        stroke: \"currentColor\",\n        \"aria-hidden\": \"true\",\n        \"data-slot\": \"icon\",\n        ref: svgRef,\n        \"aria-labelledby\": titleId\n    }, props), title ? /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"title\", {\n        id: titleId\n    }, title) : null, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"path\", {\n        strokeLinecap: \"round\",\n        strokeLinejoin: \"round\",\n        d: \"M10.5 6a7.5 7.5 0 1 0 7.5 7.5h-7.5V6Z\"\n    }), /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"path\", {\n        strokeLinecap: \"round\",\n        strokeLinejoin: \"round\",\n        d: \"M13.5 10.5H21A7.5 7.5 0 0 0 13.5 3v7.5Z\"\n    }));\n}\n_c = ChartPieIcon;\nconst ForwardRef = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(ChartPieIcon);\n_c1 = ForwardRef;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ForwardRef);\nvar _c, _c1;\n$RefreshReg$(_c, \"ChartPieIcon\");\n$RefreshReg$(_c1, \"ForwardRef\");\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ChartPieIcon.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CheckCircleIcon.js":
/*!*************************************************************************!*\
  !*** ./node_modules/@heroicons/react/24/outline/esm/CheckCircleIcon.js ***!
  \*************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n\nfunction CheckCircleIcon(param, svgRef) {\n    let { title, titleId, ...props } = param;\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"svg\", Object.assign({\n        xmlns: \"http://www.w3.org/2000/svg\",\n        fill: \"none\",\n        viewBox: \"0 0 24 24\",\n        strokeWidth: 1.5,\n        stroke: \"currentColor\",\n        \"aria-hidden\": \"true\",\n        \"data-slot\": \"icon\",\n        ref: svgRef,\n        \"aria-labelledby\": titleId\n    }, props), title ? /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"title\", {\n        id: titleId\n    }, title) : null, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"path\", {\n        strokeLinecap: \"round\",\n        strokeLinejoin: \"round\",\n        d: \"M9 12.75 11.25 15 15 9.75M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z\"\n    }));\n}\n_c = CheckCircleIcon;\nconst ForwardRef = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(CheckCircleIcon);\n_c1 = ForwardRef;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ForwardRef);\nvar _c, _c1;\n$RefreshReg$(_c, \"CheckCircleIcon\");\n$RefreshReg$(_c1, \"ForwardRef\");\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CheckCircleIcon.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CpuChipIcon.js":
/*!*********************************************************************!*\
  !*** ./node_modules/@heroicons/react/24/outline/esm/CpuChipIcon.js ***!
  \*********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n\nfunction CpuChipIcon(param, svgRef) {\n    let { title, titleId, ...props } = param;\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"svg\", Object.assign({\n        xmlns: \"http://www.w3.org/2000/svg\",\n        fill: \"none\",\n        viewBox: \"0 0 24 24\",\n        strokeWidth: 1.5,\n        stroke: \"currentColor\",\n        \"aria-hidden\": \"true\",\n        \"data-slot\": \"icon\",\n        ref: svgRef,\n        \"aria-labelledby\": titleId\n    }, props), title ? /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"title\", {\n        id: titleId\n    }, title) : null, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"path\", {\n        strokeLinecap: \"round\",\n        strokeLinejoin: \"round\",\n        d: \"M8.25 3v1.5M4.5 8.25H3m18 0h-1.5M4.5 12H3m18 0h-1.5m-15 3.75H3m18 0h-1.5M8.25 19.5V21M12 3v1.5m0 15V21m3.75-18v1.5m0 15V21m-9-1.5h10.5a2.25 2.25 0 0 0 2.25-2.25V6.75a2.25 2.25 0 0 0-2.25-2.25H6.75A2.25 2.25 0 0 0 4.5 6.75v10.5a2.25 2.25 0 0 0 2.25 2.25Zm.75-12h9v9h-9v-9Z\"\n    }));\n}\n_c = CpuChipIcon;\nconst ForwardRef = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(CpuChipIcon);\n_c1 = ForwardRef;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ForwardRef);\nvar _c, _c1;\n$RefreshReg$(_c, \"CpuChipIcon\");\n$RefreshReg$(_c1, \"ForwardRef\");\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CpuChipIcon.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CurrencyDollarIcon.js":
/*!****************************************************************************!*\
  !*** ./node_modules/@heroicons/react/24/outline/esm/CurrencyDollarIcon.js ***!
  \****************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n\nfunction CurrencyDollarIcon(param, svgRef) {\n    let { title, titleId, ...props } = param;\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"svg\", Object.assign({\n        xmlns: \"http://www.w3.org/2000/svg\",\n        fill: \"none\",\n        viewBox: \"0 0 24 24\",\n        strokeWidth: 1.5,\n        stroke: \"currentColor\",\n        \"aria-hidden\": \"true\",\n        \"data-slot\": \"icon\",\n        ref: svgRef,\n        \"aria-labelledby\": titleId\n    }, props), title ? /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"title\", {\n        id: titleId\n    }, title) : null, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"path\", {\n        strokeLinecap: \"round\",\n        strokeLinejoin: \"round\",\n        d: \"M12 6v12m-3-2.818.879.659c1.171.879 3.07.879 4.242 0 1.172-.879 1.172-2.303 0-3.182C13.536 12.219 12.768 12 12 12c-.725 0-1.45-.22-2.003-.659-1.106-.879-1.106-2.303 0-3.182s2.9-.879 4.006 0l.415.33M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z\"\n    }));\n}\n_c = CurrencyDollarIcon;\nconst ForwardRef = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(CurrencyDollarIcon);\n_c1 = ForwardRef;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ForwardRef);\nvar _c, _c1;\n$RefreshReg$(_c, \"CurrencyDollarIcon\");\n$RefreshReg$(_c1, \"ForwardRef\");\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CurrencyDollarIcon.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ExclamationTriangleIcon.js":
/*!*********************************************************************************!*\
  !*** ./node_modules/@heroicons/react/24/outline/esm/ExclamationTriangleIcon.js ***!
  \*********************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n\nfunction ExclamationTriangleIcon(param, svgRef) {\n    let { title, titleId, ...props } = param;\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"svg\", Object.assign({\n        xmlns: \"http://www.w3.org/2000/svg\",\n        fill: \"none\",\n        viewBox: \"0 0 24 24\",\n        strokeWidth: 1.5,\n        stroke: \"currentColor\",\n        \"aria-hidden\": \"true\",\n        \"data-slot\": \"icon\",\n        ref: svgRef,\n        \"aria-labelledby\": titleId\n    }, props), title ? /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"title\", {\n        id: titleId\n    }, title) : null, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"path\", {\n        strokeLinecap: \"round\",\n        strokeLinejoin: \"round\",\n        d: \"M12 9v3.75m-9.303 3.376c-.866 1.5.217 3.374 1.948 3.374h14.71c1.73 0 2.813-1.874 1.948-3.374L13.949 3.378c-.866-1.5-3.032-1.5-3.898 0L2.697 16.126ZM12 15.75h.007v.008H12v-.008Z\"\n    }));\n}\n_c = ExclamationTriangleIcon;\nconst ForwardRef = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(ExclamationTriangleIcon);\n_c1 = ForwardRef;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ForwardRef);\nvar _c, _c1;\n$RefreshReg$(_c, \"ExclamationTriangleIcon\");\n$RefreshReg$(_c1, \"ForwardRef\");\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ExclamationTriangleIcon.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/FunnelIcon.js":
/*!********************************************************************!*\
  !*** ./node_modules/@heroicons/react/24/outline/esm/FunnelIcon.js ***!
  \********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n\nfunction FunnelIcon(param, svgRef) {\n    let { title, titleId, ...props } = param;\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"svg\", Object.assign({\n        xmlns: \"http://www.w3.org/2000/svg\",\n        fill: \"none\",\n        viewBox: \"0 0 24 24\",\n        strokeWidth: 1.5,\n        stroke: \"currentColor\",\n        \"aria-hidden\": \"true\",\n        \"data-slot\": \"icon\",\n        ref: svgRef,\n        \"aria-labelledby\": titleId\n    }, props), title ? /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"title\", {\n        id: titleId\n    }, title) : null, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"path\", {\n        strokeLinecap: \"round\",\n        strokeLinejoin: \"round\",\n        d: \"M12 3c2.755 0 5.455.232 8.083.678.533.09.917.556.917 1.096v1.044a2.25 2.25 0 0 1-.659 1.591l-5.432 5.432a2.25 2.25 0 0 0-.659 1.591v2.927a2.25 2.25 0 0 1-1.244 2.013L9.75 21v-6.568a2.25 2.25 0 0 0-.659-1.591L3.659 7.409A2.25 2.25 0 0 1 3 5.818V4.774c0-.54.384-1.006.917-1.096A48.32 48.32 0 0 1 12 3Z\"\n    }));\n}\n_c = FunnelIcon;\nconst ForwardRef = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(FunnelIcon);\n_c1 = ForwardRef;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ForwardRef);\nvar _c, _c1;\n$RefreshReg$(_c, \"FunnelIcon\");\n$RefreshReg$(_c1, \"ForwardRef\");\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/FunnelIcon.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/LightBulbIcon.js":
/*!***********************************************************************!*\
  !*** ./node_modules/@heroicons/react/24/outline/esm/LightBulbIcon.js ***!
  \***********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n\nfunction LightBulbIcon(param, svgRef) {\n    let { title, titleId, ...props } = param;\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"svg\", Object.assign({\n        xmlns: \"http://www.w3.org/2000/svg\",\n        fill: \"none\",\n        viewBox: \"0 0 24 24\",\n        strokeWidth: 1.5,\n        stroke: \"currentColor\",\n        \"aria-hidden\": \"true\",\n        \"data-slot\": \"icon\",\n        ref: svgRef,\n        \"aria-labelledby\": titleId\n    }, props), title ? /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"title\", {\n        id: titleId\n    }, title) : null, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"path\", {\n        strokeLinecap: \"round\",\n        strokeLinejoin: \"round\",\n        d: \"M12 18v-5.25m0 0a6.01 6.01 0 0 0 1.5-.189m-1.5.189a6.01 6.01 0 0 1-1.5-.189m3.75 7.478a12.06 12.06 0 0 1-4.5 0m3.75 2.383a14.406 14.406 0 0 1-3 0M14.25 18v-.192c0-.983.658-1.823 1.508-2.316a7.5 7.5 0 1 0-7.517 0c.85.493 1.509 1.333 1.509 2.316V18\"\n    }));\n}\n_c = LightBulbIcon;\nconst ForwardRef = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(LightBulbIcon);\n_c1 = ForwardRef;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ForwardRef);\nvar _c, _c1;\n$RefreshReg$(_c, \"LightBulbIcon\");\n$RefreshReg$(_c1, \"ForwardRef\");\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/LightBulbIcon.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/SparklesIcon.js":
/*!**********************************************************************!*\
  !*** ./node_modules/@heroicons/react/24/outline/esm/SparklesIcon.js ***!
  \**********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n\nfunction SparklesIcon(param, svgRef) {\n    let { title, titleId, ...props } = param;\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"svg\", Object.assign({\n        xmlns: \"http://www.w3.org/2000/svg\",\n        fill: \"none\",\n        viewBox: \"0 0 24 24\",\n        strokeWidth: 1.5,\n        stroke: \"currentColor\",\n        \"aria-hidden\": \"true\",\n        \"data-slot\": \"icon\",\n        ref: svgRef,\n        \"aria-labelledby\": titleId\n    }, props), title ? /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"title\", {\n        id: titleId\n    }, title) : null, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"path\", {\n        strokeLinecap: \"round\",\n        strokeLinejoin: \"round\",\n        d: \"M9.813 15.904 9 18.75l-.813-2.846a4.5 4.5 0 0 0-3.09-3.09L2.25 12l2.846-.813a4.5 4.5 0 0 0 3.09-3.09L9 5.25l.813 2.846a4.5 4.5 0 0 0 3.09 3.09L15.75 12l-2.846.813a4.5 4.5 0 0 0-3.09 3.09ZM18.259 8.715 18 9.75l-.259-1.035a3.375 3.375 0 0 0-2.455-2.456L14.25 6l1.036-.259a3.375 3.375 0 0 0 2.455-2.456L18 2.25l.259 1.035a3.375 3.375 0 0 0 2.456 2.456L21.75 6l-1.035.259a3.375 3.375 0 0 0-2.456 2.456ZM16.894 20.567 16.5 21.75l-.394-1.183a2.25 2.25 0 0 0-1.423-1.423L13.5 18.75l1.183-.394a2.25 2.25 0 0 0 1.423-1.423l.394-1.183.394 1.183a2.25 2.25 0 0 0 1.423 1.423l1.183.394-1.183.394a2.25 2.25 0 0 0-1.423 1.423Z\"\n    }));\n}\n_c = SparklesIcon;\nconst ForwardRef = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(SparklesIcon);\n_c1 = ForwardRef;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ForwardRef);\nvar _c, _c1;\n$RefreshReg$(_c, \"SparklesIcon\");\n$RefreshReg$(_c1, \"ForwardRef\");\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9AaGVyb2ljb25zL3JlYWN0LzI0L291dGxpbmUvZXNtL1NwYXJrbGVzSWNvbi5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUErQjtBQUMvQixTQUFTQyxhQUFhLEtBSXJCLEVBQUVDLE1BQU07UUFKYSxFQUNwQkMsS0FBSyxFQUNMQyxPQUFPLEVBQ1AsR0FBR0MsT0FDSixHQUpxQjtJQUtwQixPQUFPLFdBQVcsR0FBRUwsZ0RBQW1CLENBQUMsT0FBT08sT0FBT0MsTUFBTSxDQUFDO1FBQzNEQyxPQUFPO1FBQ1BDLE1BQU07UUFDTkMsU0FBUztRQUNUQyxhQUFhO1FBQ2JDLFFBQVE7UUFDUixlQUFlO1FBQ2YsYUFBYTtRQUNiQyxLQUFLWjtRQUNMLG1CQUFtQkU7SUFDckIsR0FBR0MsUUFBUUYsUUFBUSxXQUFXLEdBQUVILGdEQUFtQixDQUFDLFNBQVM7UUFDM0RlLElBQUlYO0lBQ04sR0FBR0QsU0FBUyxNQUFNLFdBQVcsR0FBRUgsZ0RBQW1CLENBQUMsUUFBUTtRQUN6RGdCLGVBQWU7UUFDZkMsZ0JBQWdCO1FBQ2hCQyxHQUFHO0lBQ0w7QUFDRjtLQXRCU2pCO0FBdUJULE1BQU1rQixhQUFhLFdBQVcsR0FBR25CLDZDQUFnQixDQUFDQzs7QUFDbEQsaUVBQWVrQixVQUFVQSxFQUFDIiwic291cmNlcyI6WyJDOlxcUm9LZXkgQXBwXFxyb2tleS1hcHBcXG5vZGVfbW9kdWxlc1xcQGhlcm9pY29uc1xccmVhY3RcXDI0XFxvdXRsaW5lXFxlc21cXFNwYXJrbGVzSWNvbi5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgKiBhcyBSZWFjdCBmcm9tIFwicmVhY3RcIjtcbmZ1bmN0aW9uIFNwYXJrbGVzSWNvbih7XG4gIHRpdGxlLFxuICB0aXRsZUlkLFxuICAuLi5wcm9wc1xufSwgc3ZnUmVmKSB7XG4gIHJldHVybiAvKiNfX1BVUkVfXyovUmVhY3QuY3JlYXRlRWxlbWVudChcInN2Z1wiLCBPYmplY3QuYXNzaWduKHtcbiAgICB4bWxuczogXCJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2Z1wiLFxuICAgIGZpbGw6IFwibm9uZVwiLFxuICAgIHZpZXdCb3g6IFwiMCAwIDI0IDI0XCIsXG4gICAgc3Ryb2tlV2lkdGg6IDEuNSxcbiAgICBzdHJva2U6IFwiY3VycmVudENvbG9yXCIsXG4gICAgXCJhcmlhLWhpZGRlblwiOiBcInRydWVcIixcbiAgICBcImRhdGEtc2xvdFwiOiBcImljb25cIixcbiAgICByZWY6IHN2Z1JlZixcbiAgICBcImFyaWEtbGFiZWxsZWRieVwiOiB0aXRsZUlkXG4gIH0sIHByb3BzKSwgdGl0bGUgPyAvKiNfX1BVUkVfXyovUmVhY3QuY3JlYXRlRWxlbWVudChcInRpdGxlXCIsIHtcbiAgICBpZDogdGl0bGVJZFxuICB9LCB0aXRsZSkgOiBudWxsLCAvKiNfX1BVUkVfXyovUmVhY3QuY3JlYXRlRWxlbWVudChcInBhdGhcIiwge1xuICAgIHN0cm9rZUxpbmVjYXA6IFwicm91bmRcIixcbiAgICBzdHJva2VMaW5lam9pbjogXCJyb3VuZFwiLFxuICAgIGQ6IFwiTTkuODEzIDE1LjkwNCA5IDE4Ljc1bC0uODEzLTIuODQ2YTQuNSA0LjUgMCAwIDAtMy4wOS0zLjA5TDIuMjUgMTJsMi44NDYtLjgxM2E0LjUgNC41IDAgMCAwIDMuMDktMy4wOUw5IDUuMjVsLjgxMyAyLjg0NmE0LjUgNC41IDAgMCAwIDMuMDkgMy4wOUwxNS43NSAxMmwtMi44NDYuODEzYTQuNSA0LjUgMCAwIDAtMy4wOSAzLjA5Wk0xOC4yNTkgOC43MTUgMTggOS43NWwtLjI1OS0xLjAzNWEzLjM3NSAzLjM3NSAwIDAgMC0yLjQ1NS0yLjQ1NkwxNC4yNSA2bDEuMDM2LS4yNTlhMy4zNzUgMy4zNzUgMCAwIDAgMi40NTUtMi40NTZMMTggMi4yNWwuMjU5IDEuMDM1YTMuMzc1IDMuMzc1IDAgMCAwIDIuNDU2IDIuNDU2TDIxLjc1IDZsLTEuMDM1LjI1OWEzLjM3NSAzLjM3NSAwIDAgMC0yLjQ1NiAyLjQ1NlpNMTYuODk0IDIwLjU2NyAxNi41IDIxLjc1bC0uMzk0LTEuMTgzYTIuMjUgMi4yNSAwIDAgMC0xLjQyMy0xLjQyM0wxMy41IDE4Ljc1bDEuMTgzLS4zOTRhMi4yNSAyLjI1IDAgMCAwIDEuNDIzLTEuNDIzbC4zOTQtMS4xODMuMzk0IDEuMTgzYTIuMjUgMi4yNSAwIDAgMCAxLjQyMyAxLjQyM2wxLjE4My4zOTQtMS4xODMuMzk0YTIuMjUgMi4yNSAwIDAgMC0xLjQyMyAxLjQyM1pcIlxuICB9KSk7XG59XG5jb25zdCBGb3J3YXJkUmVmID0gLyojX19QVVJFX18qLyBSZWFjdC5mb3J3YXJkUmVmKFNwYXJrbGVzSWNvbik7XG5leHBvcnQgZGVmYXVsdCBGb3J3YXJkUmVmOyJdLCJuYW1lcyI6WyJSZWFjdCIsIlNwYXJrbGVzSWNvbiIsInN2Z1JlZiIsInRpdGxlIiwidGl0bGVJZCIsInByb3BzIiwiY3JlYXRlRWxlbWVudCIsIk9iamVjdCIsImFzc2lnbiIsInhtbG5zIiwiZmlsbCIsInZpZXdCb3giLCJzdHJva2VXaWR0aCIsInN0cm9rZSIsInJlZiIsImlkIiwic3Ryb2tlTGluZWNhcCIsInN0cm9rZUxpbmVqb2luIiwiZCIsIkZvcndhcmRSZWYiLCJmb3J3YXJkUmVmIl0sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/SparklesIcon.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CRoKey%20App%5C%5Crokey-app%5C%5Csrc%5C%5Capp%5C%5Canalytics%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!":
/*!****************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CRoKey%20App%5C%5Crokey-app%5C%5Csrc%5C%5Capp%5C%5Canalytics%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false! ***!
  \****************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/analytics/page.tsx */ \"(app-pages-browser)/./src/app/analytics/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvYnVpbGQvd2VicGFjay9sb2FkZXJzL25leHQtZmxpZ2h0LWNsaWVudC1lbnRyeS1sb2FkZXIuanM/bW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyQyUzQSU1QyU1Q1JvS2V5JTIwQXBwJTVDJTVDcm9rZXktYXBwJTVDJTVDc3JjJTVDJTVDYXBwJTVDJTVDYW5hbHl0aWNzJTVDJTVDcGFnZS50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0Qmc2VydmVyPWZhbHNlISIsIm1hcHBpbmdzIjoiQUFBQSxrTEFBNEYiLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkM6XFxcXFJvS2V5IEFwcFxcXFxyb2tleS1hcHBcXFxcc3JjXFxcXGFwcFxcXFxhbmFseXRpY3NcXFxccGFnZS50c3hcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CRoKey%20App%5C%5Crokey-app%5C%5Csrc%5C%5Capp%5C%5Canalytics%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js":
/*!****************************************************************************************!*\
  !*** ./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js ***!
  \****************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("/**\n * @license React\n * react-jsx-dev-runtime.development.js\n *\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n\n true &&\n  (function () {\n    function getComponentNameFromType(type) {\n      if (null == type) return null;\n      if (\"function\" === typeof type)\n        return type.$$typeof === REACT_CLIENT_REFERENCE\n          ? null\n          : type.displayName || type.name || null;\n      if (\"string\" === typeof type) return type;\n      switch (type) {\n        case REACT_FRAGMENT_TYPE:\n          return \"Fragment\";\n        case REACT_PROFILER_TYPE:\n          return \"Profiler\";\n        case REACT_STRICT_MODE_TYPE:\n          return \"StrictMode\";\n        case REACT_SUSPENSE_TYPE:\n          return \"Suspense\";\n        case REACT_SUSPENSE_LIST_TYPE:\n          return \"SuspenseList\";\n        case REACT_ACTIVITY_TYPE:\n          return \"Activity\";\n      }\n      if (\"object\" === typeof type)\n        switch (\n          (\"number\" === typeof type.tag &&\n            console.error(\n              \"Received an unexpected object in getComponentNameFromType(). This is likely a bug in React. Please file an issue.\"\n            ),\n          type.$$typeof)\n        ) {\n          case REACT_PORTAL_TYPE:\n            return \"Portal\";\n          case REACT_CONTEXT_TYPE:\n            return (type.displayName || \"Context\") + \".Provider\";\n          case REACT_CONSUMER_TYPE:\n            return (type._context.displayName || \"Context\") + \".Consumer\";\n          case REACT_FORWARD_REF_TYPE:\n            var innerType = type.render;\n            type = type.displayName;\n            type ||\n              ((type = innerType.displayName || innerType.name || \"\"),\n              (type = \"\" !== type ? \"ForwardRef(\" + type + \")\" : \"ForwardRef\"));\n            return type;\n          case REACT_MEMO_TYPE:\n            return (\n              (innerType = type.displayName || null),\n              null !== innerType\n                ? innerType\n                : getComponentNameFromType(type.type) || \"Memo\"\n            );\n          case REACT_LAZY_TYPE:\n            innerType = type._payload;\n            type = type._init;\n            try {\n              return getComponentNameFromType(type(innerType));\n            } catch (x) {}\n        }\n      return null;\n    }\n    function testStringCoercion(value) {\n      return \"\" + value;\n    }\n    function checkKeyStringCoercion(value) {\n      try {\n        testStringCoercion(value);\n        var JSCompiler_inline_result = !1;\n      } catch (e) {\n        JSCompiler_inline_result = !0;\n      }\n      if (JSCompiler_inline_result) {\n        JSCompiler_inline_result = console;\n        var JSCompiler_temp_const = JSCompiler_inline_result.error;\n        var JSCompiler_inline_result$jscomp$0 =\n          (\"function\" === typeof Symbol &&\n            Symbol.toStringTag &&\n            value[Symbol.toStringTag]) ||\n          value.constructor.name ||\n          \"Object\";\n        JSCompiler_temp_const.call(\n          JSCompiler_inline_result,\n          \"The provided key is an unsupported type %s. This value must be coerced to a string before using it here.\",\n          JSCompiler_inline_result$jscomp$0\n        );\n        return testStringCoercion(value);\n      }\n    }\n    function getTaskName(type) {\n      if (type === REACT_FRAGMENT_TYPE) return \"<>\";\n      if (\n        \"object\" === typeof type &&\n        null !== type &&\n        type.$$typeof === REACT_LAZY_TYPE\n      )\n        return \"<...>\";\n      try {\n        var name = getComponentNameFromType(type);\n        return name ? \"<\" + name + \">\" : \"<...>\";\n      } catch (x) {\n        return \"<...>\";\n      }\n    }\n    function getOwner() {\n      var dispatcher = ReactSharedInternals.A;\n      return null === dispatcher ? null : dispatcher.getOwner();\n    }\n    function UnknownOwner() {\n      return Error(\"react-stack-top-frame\");\n    }\n    function hasValidKey(config) {\n      if (hasOwnProperty.call(config, \"key\")) {\n        var getter = Object.getOwnPropertyDescriptor(config, \"key\").get;\n        if (getter && getter.isReactWarning) return !1;\n      }\n      return void 0 !== config.key;\n    }\n    function defineKeyPropWarningGetter(props, displayName) {\n      function warnAboutAccessingKey() {\n        specialPropKeyWarningShown ||\n          ((specialPropKeyWarningShown = !0),\n          console.error(\n            \"%s: `key` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://react.dev/link/special-props)\",\n            displayName\n          ));\n      }\n      warnAboutAccessingKey.isReactWarning = !0;\n      Object.defineProperty(props, \"key\", {\n        get: warnAboutAccessingKey,\n        configurable: !0\n      });\n    }\n    function elementRefGetterWithDeprecationWarning() {\n      var componentName = getComponentNameFromType(this.type);\n      didWarnAboutElementRef[componentName] ||\n        ((didWarnAboutElementRef[componentName] = !0),\n        console.error(\n          \"Accessing element.ref was removed in React 19. ref is now a regular prop. It will be removed from the JSX Element type in a future release.\"\n        ));\n      componentName = this.props.ref;\n      return void 0 !== componentName ? componentName : null;\n    }\n    function ReactElement(\n      type,\n      key,\n      self,\n      source,\n      owner,\n      props,\n      debugStack,\n      debugTask\n    ) {\n      self = props.ref;\n      type = {\n        $$typeof: REACT_ELEMENT_TYPE,\n        type: type,\n        key: key,\n        props: props,\n        _owner: owner\n      };\n      null !== (void 0 !== self ? self : null)\n        ? Object.defineProperty(type, \"ref\", {\n            enumerable: !1,\n            get: elementRefGetterWithDeprecationWarning\n          })\n        : Object.defineProperty(type, \"ref\", { enumerable: !1, value: null });\n      type._store = {};\n      Object.defineProperty(type._store, \"validated\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: 0\n      });\n      Object.defineProperty(type, \"_debugInfo\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: null\n      });\n      Object.defineProperty(type, \"_debugStack\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: debugStack\n      });\n      Object.defineProperty(type, \"_debugTask\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: debugTask\n      });\n      Object.freeze && (Object.freeze(type.props), Object.freeze(type));\n      return type;\n    }\n    function jsxDEVImpl(\n      type,\n      config,\n      maybeKey,\n      isStaticChildren,\n      source,\n      self,\n      debugStack,\n      debugTask\n    ) {\n      var children = config.children;\n      if (void 0 !== children)\n        if (isStaticChildren)\n          if (isArrayImpl(children)) {\n            for (\n              isStaticChildren = 0;\n              isStaticChildren < children.length;\n              isStaticChildren++\n            )\n              validateChildKeys(children[isStaticChildren]);\n            Object.freeze && Object.freeze(children);\n          } else\n            console.error(\n              \"React.jsx: Static children should always be an array. You are likely explicitly calling React.jsxs or React.jsxDEV. Use the Babel transform instead.\"\n            );\n        else validateChildKeys(children);\n      if (hasOwnProperty.call(config, \"key\")) {\n        children = getComponentNameFromType(type);\n        var keys = Object.keys(config).filter(function (k) {\n          return \"key\" !== k;\n        });\n        isStaticChildren =\n          0 < keys.length\n            ? \"{key: someKey, \" + keys.join(\": ..., \") + \": ...}\"\n            : \"{key: someKey}\";\n        didWarnAboutKeySpread[children + isStaticChildren] ||\n          ((keys =\n            0 < keys.length ? \"{\" + keys.join(\": ..., \") + \": ...}\" : \"{}\"),\n          console.error(\n            'A props object containing a \"key\" prop is being spread into JSX:\\n  let props = %s;\\n  <%s {...props} />\\nReact keys must be passed directly to JSX without using spread:\\n  let props = %s;\\n  <%s key={someKey} {...props} />',\n            isStaticChildren,\n            children,\n            keys,\n            children\n          ),\n          (didWarnAboutKeySpread[children + isStaticChildren] = !0));\n      }\n      children = null;\n      void 0 !== maybeKey &&\n        (checkKeyStringCoercion(maybeKey), (children = \"\" + maybeKey));\n      hasValidKey(config) &&\n        (checkKeyStringCoercion(config.key), (children = \"\" + config.key));\n      if (\"key\" in config) {\n        maybeKey = {};\n        for (var propName in config)\n          \"key\" !== propName && (maybeKey[propName] = config[propName]);\n      } else maybeKey = config;\n      children &&\n        defineKeyPropWarningGetter(\n          maybeKey,\n          \"function\" === typeof type\n            ? type.displayName || type.name || \"Unknown\"\n            : type\n        );\n      return ReactElement(\n        type,\n        children,\n        self,\n        source,\n        getOwner(),\n        maybeKey,\n        debugStack,\n        debugTask\n      );\n    }\n    function validateChildKeys(node) {\n      \"object\" === typeof node &&\n        null !== node &&\n        node.$$typeof === REACT_ELEMENT_TYPE &&\n        node._store &&\n        (node._store.validated = 1);\n    }\n    var React = __webpack_require__(/*! next/dist/compiled/react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\"),\n      REACT_ELEMENT_TYPE = Symbol.for(\"react.transitional.element\"),\n      REACT_PORTAL_TYPE = Symbol.for(\"react.portal\"),\n      REACT_FRAGMENT_TYPE = Symbol.for(\"react.fragment\"),\n      REACT_STRICT_MODE_TYPE = Symbol.for(\"react.strict_mode\"),\n      REACT_PROFILER_TYPE = Symbol.for(\"react.profiler\");\n    Symbol.for(\"react.provider\");\n    var REACT_CONSUMER_TYPE = Symbol.for(\"react.consumer\"),\n      REACT_CONTEXT_TYPE = Symbol.for(\"react.context\"),\n      REACT_FORWARD_REF_TYPE = Symbol.for(\"react.forward_ref\"),\n      REACT_SUSPENSE_TYPE = Symbol.for(\"react.suspense\"),\n      REACT_SUSPENSE_LIST_TYPE = Symbol.for(\"react.suspense_list\"),\n      REACT_MEMO_TYPE = Symbol.for(\"react.memo\"),\n      REACT_LAZY_TYPE = Symbol.for(\"react.lazy\"),\n      REACT_ACTIVITY_TYPE = Symbol.for(\"react.activity\"),\n      REACT_CLIENT_REFERENCE = Symbol.for(\"react.client.reference\"),\n      ReactSharedInternals =\n        React.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,\n      hasOwnProperty = Object.prototype.hasOwnProperty,\n      isArrayImpl = Array.isArray,\n      createTask = console.createTask\n        ? console.createTask\n        : function () {\n            return null;\n          };\n    React = {\n      \"react-stack-bottom-frame\": function (callStackForError) {\n        return callStackForError();\n      }\n    };\n    var specialPropKeyWarningShown;\n    var didWarnAboutElementRef = {};\n    var unknownOwnerDebugStack = React[\"react-stack-bottom-frame\"].bind(\n      React,\n      UnknownOwner\n    )();\n    var unknownOwnerDebugTask = createTask(getTaskName(UnknownOwner));\n    var didWarnAboutKeySpread = {};\n    exports.Fragment = REACT_FRAGMENT_TYPE;\n    exports.jsxDEV = function (\n      type,\n      config,\n      maybeKey,\n      isStaticChildren,\n      source,\n      self\n    ) {\n      var trackActualOwner =\n        1e4 > ReactSharedInternals.recentlyCreatedOwnerStacks++;\n      return jsxDEVImpl(\n        type,\n        config,\n        maybeKey,\n        isStaticChildren,\n        source,\n        self,\n        trackActualOwner\n          ? Error(\"react-stack-top-frame\")\n          : unknownOwnerDebugStack,\n        trackActualOwner ? createTask(getTaskName(type)) : unknownOwnerDebugTask\n      );\n    };\n  })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js":
/*!******************************************************************!*\
  !*** ./node_modules/next/dist/compiled/react/jsx-dev-runtime.js ***!
  \******************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("\n\nif (false) {} else {\n  module.exports = __webpack_require__(/*! ./cjs/react-jsx-dev-runtime.development.js */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js\");\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY29tcGlsZWQvcmVhY3QvanN4LWRldi1ydW50aW1lLmpzIiwibWFwcGluZ3MiOiJBQUFhOztBQUViLElBQUksS0FBcUMsRUFBRSxFQUUxQyxDQUFDO0FBQ0YsRUFBRSw4TEFBc0U7QUFDeEUiLCJzb3VyY2VzIjpbIkM6XFxSb0tleSBBcHBcXHJva2V5LWFwcFxcbm9kZV9tb2R1bGVzXFxuZXh0XFxkaXN0XFxjb21waWxlZFxccmVhY3RcXGpzeC1kZXYtcnVudGltZS5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIHN0cmljdCc7XG5cbmlmIChwcm9jZXNzLmVudi5OT0RFX0VOViA9PT0gJ3Byb2R1Y3Rpb24nKSB7XG4gIG1vZHVsZS5leHBvcnRzID0gcmVxdWlyZSgnLi9janMvcmVhY3QtanN4LWRldi1ydW50aW1lLnByb2R1Y3Rpb24uanMnKTtcbn0gZWxzZSB7XG4gIG1vZHVsZS5leHBvcnRzID0gcmVxdWlyZSgnLi9janMvcmVhY3QtanN4LWRldi1ydW50aW1lLmRldmVsb3BtZW50LmpzJyk7XG59XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/app/analytics/page.tsx":
/*!************************************!*\
  !*** ./src/app/analytics/page.tsx ***!
  \************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AnalyticsPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_ArrowTrendingDownIcon_ArrowTrendingUpIcon_BellIcon_BoltIcon_CalendarIcon_ChartBarIcon_ChartPieIcon_CheckCircleIcon_CpuChipIcon_CurrencyDollarIcon_ExclamationTriangleIcon_FunnelIcon_LightBulbIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowTrendingDownIcon,ArrowTrendingUpIcon,BellIcon,BoltIcon,CalendarIcon,ChartBarIcon,ChartPieIcon,CheckCircleIcon,CpuChipIcon,CurrencyDollarIcon,ExclamationTriangleIcon,FunnelIcon,LightBulbIcon,SparklesIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CurrencyDollarIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowTrendingDownIcon_ArrowTrendingUpIcon_BellIcon_BoltIcon_CalendarIcon_ChartBarIcon_ChartPieIcon_CheckCircleIcon_CpuChipIcon_CurrencyDollarIcon_ExclamationTriangleIcon_FunnelIcon_LightBulbIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowTrendingDownIcon,ArrowTrendingUpIcon,BellIcon,BoltIcon,CalendarIcon,ChartBarIcon,ChartPieIcon,CheckCircleIcon,CpuChipIcon,CurrencyDollarIcon,ExclamationTriangleIcon,FunnelIcon,LightBulbIcon,SparklesIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CheckCircleIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowTrendingDownIcon_ArrowTrendingUpIcon_BellIcon_BoltIcon_CalendarIcon_ChartBarIcon_ChartPieIcon_CheckCircleIcon_CpuChipIcon_CurrencyDollarIcon_ExclamationTriangleIcon_FunnelIcon_LightBulbIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowTrendingDownIcon,ArrowTrendingUpIcon,BellIcon,BoltIcon,CalendarIcon,ChartBarIcon,ChartPieIcon,CheckCircleIcon,CpuChipIcon,CurrencyDollarIcon,ExclamationTriangleIcon,FunnelIcon,LightBulbIcon,SparklesIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CpuChipIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowTrendingDownIcon_ArrowTrendingUpIcon_BellIcon_BoltIcon_CalendarIcon_ChartBarIcon_ChartPieIcon_CheckCircleIcon_CpuChipIcon_CurrencyDollarIcon_ExclamationTriangleIcon_FunnelIcon_LightBulbIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowTrendingDownIcon,ArrowTrendingUpIcon,BellIcon,BoltIcon,CalendarIcon,ChartBarIcon,ChartPieIcon,CheckCircleIcon,CpuChipIcon,CurrencyDollarIcon,ExclamationTriangleIcon,FunnelIcon,LightBulbIcon,SparklesIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ChartBarIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowTrendingDownIcon_ArrowTrendingUpIcon_BellIcon_BoltIcon_CalendarIcon_ChartBarIcon_ChartPieIcon_CheckCircleIcon_CpuChipIcon_CurrencyDollarIcon_ExclamationTriangleIcon_FunnelIcon_LightBulbIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowTrendingDownIcon,ArrowTrendingUpIcon,BellIcon,BoltIcon,CalendarIcon,ChartBarIcon,ChartPieIcon,CheckCircleIcon,CpuChipIcon,CurrencyDollarIcon,ExclamationTriangleIcon,FunnelIcon,LightBulbIcon,SparklesIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ChartPieIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowTrendingDownIcon_ArrowTrendingUpIcon_BellIcon_BoltIcon_CalendarIcon_ChartBarIcon_ChartPieIcon_CheckCircleIcon_CpuChipIcon_CurrencyDollarIcon_ExclamationTriangleIcon_FunnelIcon_LightBulbIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowTrendingDownIcon,ArrowTrendingUpIcon,BellIcon,BoltIcon,CalendarIcon,ChartBarIcon,ChartPieIcon,CheckCircleIcon,CpuChipIcon,CurrencyDollarIcon,ExclamationTriangleIcon,FunnelIcon,LightBulbIcon,SparklesIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/FunnelIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowTrendingDownIcon_ArrowTrendingUpIcon_BellIcon_BoltIcon_CalendarIcon_ChartBarIcon_ChartPieIcon_CheckCircleIcon_CpuChipIcon_CurrencyDollarIcon_ExclamationTriangleIcon_FunnelIcon_LightBulbIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowTrendingDownIcon,ArrowTrendingUpIcon,BellIcon,BoltIcon,CalendarIcon,ChartBarIcon,ChartPieIcon,CheckCircleIcon,CpuChipIcon,CurrencyDollarIcon,ExclamationTriangleIcon,FunnelIcon,LightBulbIcon,SparklesIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ArrowTrendingUpIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowTrendingDownIcon_ArrowTrendingUpIcon_BellIcon_BoltIcon_CalendarIcon_ChartBarIcon_ChartPieIcon_CheckCircleIcon_CpuChipIcon_CurrencyDollarIcon_ExclamationTriangleIcon_FunnelIcon_LightBulbIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowTrendingDownIcon,ArrowTrendingUpIcon,BellIcon,BoltIcon,CalendarIcon,ChartBarIcon,ChartPieIcon,CheckCircleIcon,CpuChipIcon,CurrencyDollarIcon,ExclamationTriangleIcon,FunnelIcon,LightBulbIcon,SparklesIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ArrowTrendingDownIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowTrendingDownIcon_ArrowTrendingUpIcon_BellIcon_BoltIcon_CalendarIcon_ChartBarIcon_ChartPieIcon_CheckCircleIcon_CpuChipIcon_CurrencyDollarIcon_ExclamationTriangleIcon_FunnelIcon_LightBulbIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowTrendingDownIcon,ArrowTrendingUpIcon,BellIcon,BoltIcon,CalendarIcon,ChartBarIcon,ChartPieIcon,CheckCircleIcon,CpuChipIcon,CurrencyDollarIcon,ExclamationTriangleIcon,FunnelIcon,LightBulbIcon,SparklesIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/SparklesIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowTrendingDownIcon_ArrowTrendingUpIcon_BellIcon_BoltIcon_CalendarIcon_ChartBarIcon_ChartPieIcon_CheckCircleIcon_CpuChipIcon_CurrencyDollarIcon_ExclamationTriangleIcon_FunnelIcon_LightBulbIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowTrendingDownIcon,ArrowTrendingUpIcon,BellIcon,BoltIcon,CalendarIcon,ChartBarIcon,ChartPieIcon,CheckCircleIcon,CpuChipIcon,CurrencyDollarIcon,ExclamationTriangleIcon,FunnelIcon,LightBulbIcon,SparklesIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ExclamationTriangleIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowTrendingDownIcon_ArrowTrendingUpIcon_BellIcon_BoltIcon_CalendarIcon_ChartBarIcon_ChartPieIcon_CheckCircleIcon_CpuChipIcon_CurrencyDollarIcon_ExclamationTriangleIcon_FunnelIcon_LightBulbIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowTrendingDownIcon,ArrowTrendingUpIcon,BellIcon,BoltIcon,CalendarIcon,ChartBarIcon,ChartPieIcon,CheckCircleIcon,CpuChipIcon,CurrencyDollarIcon,ExclamationTriangleIcon,FunnelIcon,LightBulbIcon,SparklesIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/BoltIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowTrendingDownIcon_ArrowTrendingUpIcon_BellIcon_BoltIcon_CalendarIcon_ChartBarIcon_ChartPieIcon_CheckCircleIcon_CpuChipIcon_CurrencyDollarIcon_ExclamationTriangleIcon_FunnelIcon_LightBulbIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowTrendingDownIcon,ArrowTrendingUpIcon,BellIcon,BoltIcon,CalendarIcon,ChartBarIcon,ChartPieIcon,CheckCircleIcon,CpuChipIcon,CurrencyDollarIcon,ExclamationTriangleIcon,FunnelIcon,LightBulbIcon,SparklesIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/BellIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowTrendingDownIcon_ArrowTrendingUpIcon_BellIcon_BoltIcon_CalendarIcon_ChartBarIcon_ChartPieIcon_CheckCircleIcon_CpuChipIcon_CurrencyDollarIcon_ExclamationTriangleIcon_FunnelIcon_LightBulbIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowTrendingDownIcon,ArrowTrendingUpIcon,BellIcon,BoltIcon,CalendarIcon,ChartBarIcon,ChartPieIcon,CheckCircleIcon,CpuChipIcon,CurrencyDollarIcon,ExclamationTriangleIcon,FunnelIcon,LightBulbIcon,SparklesIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/LightBulbIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowTrendingDownIcon_ArrowTrendingUpIcon_BellIcon_BoltIcon_CalendarIcon_ChartBarIcon_ChartPieIcon_CheckCircleIcon_CpuChipIcon_CurrencyDollarIcon_ExclamationTriangleIcon_FunnelIcon_LightBulbIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowTrendingDownIcon,ArrowTrendingUpIcon,BellIcon,BoltIcon,CalendarIcon,ChartBarIcon,ChartPieIcon,CheckCircleIcon,CpuChipIcon,CurrencyDollarIcon,ExclamationTriangleIcon,FunnelIcon,LightBulbIcon,SparklesIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CalendarIcon.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\nfunction AnalyticsPageContent() {\n    _s();\n    const [analyticsData, setAnalyticsData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [modelAnalytics, setModelAnalytics] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [configAnalytics, setConfigAnalytics] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [timeSeriesData, setTimeSeriesData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [previousPeriodData, setPreviousPeriodData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [customConfigs, setCustomConfigs] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Filters\n    const [timeRange, setTimeRange] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('30');\n    const [selectedConfig, setSelectedConfig] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [showFilters, setShowFilters] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [startDate, setStartDate] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [endDate, setEndDate] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    // Budget settings\n    const [monthlyBudget, setMonthlyBudget] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(100); // Default $100/month\n    const [showBudgetSettings, setShowBudgetSettings] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AnalyticsPageContent.useEffect\": ()=>{\n            fetchCustomConfigs();\n        }\n    }[\"AnalyticsPageContent.useEffect\"], []);\n    const fetchCustomConfigs = async ()=>{\n        try {\n            const response = await fetch('/api/custom-configs');\n            if (response.ok) {\n                const configs = await response.json();\n                setCustomConfigs(configs);\n            }\n        } catch (err) {\n            console.error('Error fetching configs:', err);\n        }\n    };\n    const fetchAnalyticsData = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"AnalyticsPageContent.useCallback[fetchAnalyticsData]\": async ()=>{\n            try {\n                setLoading(true);\n                setError(null);\n                // Build query parameters for current period\n                const params = new URLSearchParams();\n                let currentStartDate;\n                let currentEndDate = new Date();\n                if (startDate && endDate) {\n                    currentStartDate = new Date(startDate);\n                    currentEndDate = new Date(endDate);\n                    params.append('startDate', currentStartDate.toISOString());\n                    params.append('endDate', currentEndDate.toISOString());\n                } else if (timeRange) {\n                    currentStartDate = new Date();\n                    currentStartDate.setDate(currentStartDate.getDate() - parseInt(timeRange));\n                    params.append('startDate', currentStartDate.toISOString());\n                }\n                if (selectedConfig) {\n                    params.append('customApiConfigId', selectedConfig);\n                }\n                // Build query parameters for previous period (for comparison)\n                const prevParams = new URLSearchParams();\n                const periodLength = currentEndDate.getTime() - currentStartDate.getTime();\n                const prevStartDate = new Date(currentStartDate.getTime() - periodLength);\n                const prevEndDate = new Date(currentStartDate.getTime());\n                prevParams.append('startDate', prevStartDate.toISOString());\n                prevParams.append('endDate', prevEndDate.toISOString());\n                if (selectedConfig) {\n                    prevParams.append('customApiConfigId', selectedConfig);\n                }\n                // Fetch multiple analytics views\n                const [providerResponse, modelResponse, timeSeriesResponse, previousPeriodResponse] = await Promise.all([\n                    fetch(\"/api/analytics/summary?\".concat(params.toString(), \"&groupBy=provider\")),\n                    fetch(\"/api/analytics/summary?\".concat(params.toString(), \"&groupBy=model\")),\n                    fetch(\"/api/analytics/summary?\".concat(params.toString(), \"&groupBy=day\")),\n                    fetch(\"/api/analytics/summary?\".concat(prevParams.toString(), \"&groupBy=day\"))\n                ]);\n                if (!providerResponse.ok || !modelResponse.ok || !timeSeriesResponse.ok) {\n                    throw new Error('Failed to fetch analytics data');\n                }\n                const providerData = await providerResponse.json();\n                const modelData = await modelResponse.json();\n                const timeSeriesResult = await timeSeriesResponse.json();\n                const previousPeriodResult = previousPeriodResponse.ok ? await previousPeriodResponse.json() : null;\n                setAnalyticsData(providerData);\n                setModelAnalytics(modelData);\n                setPreviousPeriodData(previousPeriodResult);\n                // Process time series data\n                const processedTimeSeries = timeSeriesResult.grouped_data.map({\n                    \"AnalyticsPageContent.useCallback[fetchAnalyticsData].processedTimeSeries\": (item)=>({\n                            period: item.period,\n                            cost: item.cost,\n                            requests: item.requests,\n                            tokens: item.input_tokens + item.output_tokens\n                        })\n                }[\"AnalyticsPageContent.useCallback[fetchAnalyticsData].processedTimeSeries\"]);\n                setTimeSeriesData(processedTimeSeries);\n            } catch (err) {\n                setError(err.message);\n                console.error('Error fetching analytics:', err);\n            } finally{\n                setLoading(false);\n            }\n        }\n    }[\"AnalyticsPageContent.useCallback[fetchAnalyticsData]\"], [\n        timeRange,\n        selectedConfig,\n        startDate,\n        endDate\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AnalyticsPageContent.useEffect\": ()=>{\n            fetchAnalyticsData();\n        }\n    }[\"AnalyticsPageContent.useEffect\"], [\n        fetchAnalyticsData,\n        timeRange,\n        selectedConfig,\n        startDate,\n        endDate\n    ]);\n    const formatCurrency = (amount)=>{\n        return new Intl.NumberFormat('en-US', {\n            style: 'currency',\n            currency: 'USD',\n            minimumFractionDigits: 2,\n            maximumFractionDigits: 6\n        }).format(amount);\n    };\n    const formatNumber = (num)=>{\n        return new Intl.NumberFormat('en-US').format(num);\n    };\n    const calculateTrend = (current, previous)=>{\n        if (previous === 0) return {\n            percentage: 0,\n            isPositive: true\n        };\n        const percentage = (current - previous) / previous * 100;\n        return {\n            percentage: Math.abs(percentage),\n            isPositive: percentage >= 0\n        };\n    };\n    const generateCostAlerts = ()=>{\n        const alerts = [];\n        const summary = analyticsData === null || analyticsData === void 0 ? void 0 : analyticsData.summary;\n        if (!summary) return alerts;\n        // Budget alert\n        const projectedMonthlyCost = summary.total_cost / parseInt(timeRange) * 30;\n        if (projectedMonthlyCost > monthlyBudget * 0.8) {\n            alerts.push({\n                type: projectedMonthlyCost > monthlyBudget ? 'danger' : 'warning',\n                title: projectedMonthlyCost > monthlyBudget ? 'Budget Exceeded' : 'Budget Warning',\n                message: \"Projected monthly cost: \".concat(formatCurrency(projectedMonthlyCost), \" (Budget: \").concat(formatCurrency(monthlyBudget), \")\"),\n                value: \"\".concat((projectedMonthlyCost / monthlyBudget * 100).toFixed(0), \"%\")\n            });\n        }\n        // Success rate alert\n        if (summary.success_rate < 95) {\n            alerts.push({\n                type: summary.success_rate < 90 ? 'danger' : 'warning',\n                title: 'Low Success Rate',\n                message: \"Current success rate is \".concat(summary.success_rate.toFixed(1), \"%. Consider reviewing failed requests.\"),\n                value: \"\".concat(summary.success_rate.toFixed(1), \"%\")\n            });\n        }\n        // High cost per request alert\n        if (summary.average_cost_per_request > 0.01) {\n            alerts.push({\n                type: 'warning',\n                title: 'High Cost Per Request',\n                message: \"Average cost per request is \".concat(formatCurrency(summary.average_cost_per_request), \". Consider optimizing model usage.\"),\n                value: formatCurrency(summary.average_cost_per_request)\n            });\n        }\n        return alerts;\n    };\n    const generateRecommendations = ()=>{\n        const recommendations = [];\n        const summary = analyticsData === null || analyticsData === void 0 ? void 0 : analyticsData.summary;\n        const providers = (analyticsData === null || analyticsData === void 0 ? void 0 : analyticsData.grouped_data) || [];\n        const models = (modelAnalytics === null || modelAnalytics === void 0 ? void 0 : modelAnalytics.grouped_data) || [];\n        if (!summary) return recommendations;\n        // Cost optimization recommendations\n        const mostExpensiveModel = models.sort((a, b)=>b.cost - a.cost)[0];\n        if (mostExpensiveModel && mostExpensiveModel.cost > summary.total_cost * 0.3) {\n            const potentialSavings = mostExpensiveModel.cost * 0.2; // Assume 20% savings possible\n            recommendations.push({\n                type: 'cost_optimization',\n                title: 'Optimize Expensive Model Usage',\n                description: \"\".concat(mostExpensiveModel.name, \" accounts for \").concat((mostExpensiveModel.cost / summary.total_cost * 100).toFixed(1), \"% of your costs. Consider using a more cost-effective alternative for simpler tasks.\"),\n                potential_savings: potentialSavings,\n                icon: _barrel_optimize_names_ArrowTrendingDownIcon_ArrowTrendingUpIcon_BellIcon_BoltIcon_CalendarIcon_ChartBarIcon_ChartPieIcon_CheckCircleIcon_CpuChipIcon_CurrencyDollarIcon_ExclamationTriangleIcon_FunnelIcon_LightBulbIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__[\"default\"]\n            });\n        }\n        // Performance recommendations\n        if (summary.success_rate < 98) {\n            recommendations.push({\n                type: 'performance',\n                title: 'Improve Request Reliability',\n                description: \"Your success rate is \".concat(summary.success_rate.toFixed(1), \"%. Implement retry logic and error handling to improve reliability.\"),\n                icon: _barrel_optimize_names_ArrowTrendingDownIcon_ArrowTrendingUpIcon_BellIcon_BoltIcon_CalendarIcon_ChartBarIcon_ChartPieIcon_CheckCircleIcon_CpuChipIcon_CurrencyDollarIcon_ExclamationTriangleIcon_FunnelIcon_LightBulbIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__[\"default\"]\n            });\n        }\n        // Efficiency recommendations\n        const avgTokensPerRequest = (summary.total_input_tokens + summary.total_output_tokens) / summary.total_requests;\n        if (avgTokensPerRequest > 1000) {\n            recommendations.push({\n                type: 'efficiency',\n                title: 'Optimize Token Usage',\n                description: \"Average \".concat(formatNumber(avgTokensPerRequest), \" tokens per request. Consider breaking down large prompts or using more efficient models.\"),\n                icon: _barrel_optimize_names_ArrowTrendingDownIcon_ArrowTrendingUpIcon_BellIcon_BoltIcon_CalendarIcon_ChartBarIcon_ChartPieIcon_CheckCircleIcon_CpuChipIcon_CurrencyDollarIcon_ExclamationTriangleIcon_FunnelIcon_LightBulbIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"]\n            });\n        }\n        return recommendations;\n    };\n    const resetFilters = ()=>{\n        setTimeRange('30');\n        setSelectedConfig('');\n        setStartDate('');\n        setEndDate('');\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"animate-pulse\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"h-8 bg-gray-200 rounded w-1/3 mb-4\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                            lineNumber: 313,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"h-4 bg-gray-200 rounded w-1/2\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                            lineNumber: 314,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                    lineNumber: 312,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\",\n                    children: [\n                        ...Array(4)\n                    ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"card p-6 animate-pulse\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"h-4 bg-gray-200 rounded w-1/2 mb-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                    lineNumber: 319,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"h-8 bg-gray-200 rounded w-3/4 mb-2\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                    lineNumber: 320,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"h-3 bg-gray-200 rounded w-1/3\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                    lineNumber: 321,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, i, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                            lineNumber: 318,\n                            columnNumber: 13\n                        }, this))\n                }, void 0, false, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                    lineNumber: 316,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n            lineNumber: 311,\n            columnNumber: 7\n        }, this);\n    }\n    if (error) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                    className: \"text-4xl font-bold mb-6\",\n                    children: \"\\uD83D\\uDCCA Advanced Analytics\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                    lineNumber: 332,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"card p-6 text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-red-600 mb-4\",\n                            children: [\n                                \"Error loading analytics: \",\n                                error\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                            lineNumber: 334,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: fetchAnalyticsData,\n                            className: \"btn-primary\",\n                            children: \"Retry\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                            lineNumber: 335,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                    lineNumber: 333,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n            lineNumber: 331,\n            columnNumber: 7\n        }, this);\n    }\n    const summary = analyticsData === null || analyticsData === void 0 ? void 0 : analyticsData.summary;\n    const costEfficiency = summary ? summary.total_cost / Math.max(summary.successful_requests, 1) : 0;\n    const projectedMonthlyCost = summary ? summary.total_cost / parseInt(timeRange) * 30 : 0;\n    // Generate insights\n    const costAlerts = generateCostAlerts();\n    const recommendations = generateRecommendations();\n    // Calculate trends vs previous period\n    const previousSummary = previousPeriodData === null || previousPeriodData === void 0 ? void 0 : previousPeriodData.summary;\n    const costTrend = previousSummary ? calculateTrend((summary === null || summary === void 0 ? void 0 : summary.total_cost) || 0, previousSummary.total_cost) : null;\n    const requestTrend = previousSummary ? calculateTrend((summary === null || summary === void 0 ? void 0 : summary.total_requests) || 0, previousSummary.total_requests) : null;\n    // Professional Line Chart Component (like reference)\n    const ProfessionalLineChart = (param)=>{\n        let { data } = param;\n        if (!data.length) {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"h-64 flex items-center justify-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowTrendingDownIcon_ArrowTrendingUpIcon_BellIcon_BoltIcon_CalendarIcon_ChartBarIcon_ChartPieIcon_CheckCircleIcon_CpuChipIcon_CurrencyDollarIcon_ExclamationTriangleIcon_FunnelIcon_LightBulbIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                            className: \"h-12 w-12 mx-auto mb-4 text-gray-300\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                            lineNumber: 362,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-500\",\n                            children: \"No data available\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                            lineNumber: 363,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                    lineNumber: 361,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                lineNumber: 360,\n                columnNumber: 9\n            }, this);\n        }\n        const maxCost = Math.max(...data.map((d)=>d.cost));\n        const minCost = Math.min(...data.map((d)=>d.cost));\n        const maxRequests = Math.max(...data.map((d)=>d.requests));\n        const minRequests = Math.min(...data.map((d)=>d.requests));\n        const costRange = maxCost - minCost || 1;\n        const requestRange = maxRequests - minRequests || 1;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"relative h-64 w-full\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                className: \"w-full h-full\",\n                viewBox: \"0 0 600 200\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"defs\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"linearGradient\", {\n                                id: \"costGradient\",\n                                x1: \"0%\",\n                                y1: \"0%\",\n                                x2: \"0%\",\n                                y2: \"100%\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"stop\", {\n                                        offset: \"0%\",\n                                        stopColor: \"#8b5cf6\",\n                                        stopOpacity: \"0.3\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                        lineNumber: 383,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"stop\", {\n                                        offset: \"100%\",\n                                        stopColor: \"#8b5cf6\",\n                                        stopOpacity: \"0.05\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                        lineNumber: 384,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                lineNumber: 382,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"linearGradient\", {\n                                id: \"requestGradient\",\n                                x1: \"0%\",\n                                y1: \"0%\",\n                                x2: \"0%\",\n                                y2: \"100%\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"stop\", {\n                                        offset: \"0%\",\n                                        stopColor: \"#06b6d4\",\n                                        stopOpacity: \"0.3\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                        lineNumber: 387,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"stop\", {\n                                        offset: \"100%\",\n                                        stopColor: \"#06b6d4\",\n                                        stopOpacity: \"0.05\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                        lineNumber: 388,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                lineNumber: 386,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pattern\", {\n                                id: \"grid\",\n                                width: \"40\",\n                                height: \"40\",\n                                patternUnits: \"userSpaceOnUse\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    d: \"M 40 0 L 0 0 0 40\",\n                                    fill: \"none\",\n                                    stroke: \"#f1f5f9\",\n                                    strokeWidth: \"0.5\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                    lineNumber: 393,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                lineNumber: 392,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                        lineNumber: 380,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"rect\", {\n                        width: \"600\",\n                        height: \"200\",\n                        fill: \"url(#grid)\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                        lineNumber: 398,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"polygon\", {\n                        fill: \"url(#costGradient)\",\n                        points: \"0,200 \".concat(data.map((d, i)=>{\n                            const x = i / Math.max(data.length - 1, 1) * 600;\n                            const y = 200 - (d.cost - minCost) / costRange * 160;\n                            return \"\".concat(x, \",\").concat(y);\n                        }).join(' '), \" 600,200\")\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                        lineNumber: 401,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"polygon\", {\n                        fill: \"url(#requestGradient)\",\n                        points: \"0,200 \".concat(data.map((d, i)=>{\n                            const x = i / Math.max(data.length - 1, 1) * 600;\n                            const y = 200 - (d.requests - minRequests) / requestRange * 160;\n                            return \"\".concat(x, \",\").concat(y);\n                        }).join(' '), \" 600,200\")\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                        lineNumber: 411,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"polyline\", {\n                        fill: \"none\",\n                        stroke: \"#8b5cf6\",\n                        strokeWidth: \"2.5\",\n                        strokeLinecap: \"round\",\n                        strokeLinejoin: \"round\",\n                        points: data.map((d, i)=>{\n                            const x = i / Math.max(data.length - 1, 1) * 600;\n                            const y = 200 - (d.cost - minCost) / costRange * 160;\n                            return \"\".concat(x, \",\").concat(y);\n                        }).join(' ')\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                        lineNumber: 421,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"polyline\", {\n                        fill: \"none\",\n                        stroke: \"#06b6d4\",\n                        strokeWidth: \"2.5\",\n                        strokeLinecap: \"round\",\n                        strokeLinejoin: \"round\",\n                        points: data.map((d, i)=>{\n                            const x = i / Math.max(data.length - 1, 1) * 600;\n                            const y = 200 - (d.requests - minRequests) / requestRange * 160;\n                            return \"\".concat(x, \",\").concat(y);\n                        }).join(' ')\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                        lineNumber: 435,\n                        columnNumber: 11\n                    }, this),\n                    data.map((d, i)=>{\n                        const x = i / Math.max(data.length - 1, 1) * 600;\n                        const costY = 200 - (d.cost - minCost) / costRange * 160;\n                        const requestY = 200 - (d.requests - minRequests) / requestRange * 160;\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"g\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                    cx: x,\n                                    cy: costY,\n                                    r: \"3\",\n                                    fill: \"#8b5cf6\",\n                                    stroke: \"white\",\n                                    strokeWidth: \"2\",\n                                    className: \"hover:r-5 transition-all duration-200 cursor-pointer\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"title\", {\n                                        children: \"\".concat(d.period, \": \").concat(formatCurrency(d.cost))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                        lineNumber: 464,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                    lineNumber: 455,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                    cx: x,\n                                    cy: requestY,\n                                    r: \"3\",\n                                    fill: \"#06b6d4\",\n                                    stroke: \"white\",\n                                    strokeWidth: \"2\",\n                                    className: \"hover:r-5 transition-all duration-200 cursor-pointer\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"title\", {\n                                        children: \"\".concat(d.period, \": \").concat(formatNumber(d.requests), \" requests\")\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                        lineNumber: 475,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                    lineNumber: 466,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, i, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                            lineNumber: 454,\n                            columnNumber: 15\n                        }, this);\n                    })\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                lineNumber: 379,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n            lineNumber: 378,\n            columnNumber: 7\n        }, this);\n    };\n    // Professional Donut Chart Component (like reference)\n    const ProfessionalDonutChart = (param)=>{\n        let { data, total } = param;\n        if (!data.length) {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"h-48 flex items-center justify-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowTrendingDownIcon_ArrowTrendingUpIcon_BellIcon_BoltIcon_CalendarIcon_ChartBarIcon_ChartPieIcon_CheckCircleIcon_CpuChipIcon_CurrencyDollarIcon_ExclamationTriangleIcon_FunnelIcon_LightBulbIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                            className: \"h-12 w-12 mx-auto mb-4 text-gray-300\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                            lineNumber: 491,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-500\",\n                            children: \"No data available\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                            lineNumber: 492,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                    lineNumber: 490,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                lineNumber: 489,\n                columnNumber: 9\n            }, this);\n        }\n        // Professional color palette like the reference\n        const colors = [\n            '#f43f5e',\n            '#8b5cf6',\n            '#06b6d4',\n            '#10b981',\n            '#f59e0b',\n            '#ef4444'\n        ];\n        const radius = 70;\n        const strokeWidth = 16;\n        const normalizedRadius = radius - strokeWidth * 0.5;\n        const circumference = normalizedRadius * 2 * Math.PI;\n        let cumulativePercentage = 0;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex flex-col items-center\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"relative mb-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                            width: \"160\",\n                            height: \"160\",\n                            className: \"transform -rotate-90\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                    cx: \"80\",\n                                    cy: \"80\",\n                                    r: normalizedRadius,\n                                    stroke: \"#f8fafc\",\n                                    strokeWidth: strokeWidth,\n                                    fill: \"transparent\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                    lineNumber: 512,\n                                    columnNumber: 13\n                                }, this),\n                                data.map((item, index)=>{\n                                    const percentage = item.cost / total * 100;\n                                    const strokeDasharray = \"\".concat(percentage / 100 * circumference, \" \").concat(circumference);\n                                    const strokeDashoffset = -(cumulativePercentage / 100 * circumference);\n                                    cumulativePercentage += percentage;\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                        cx: \"80\",\n                                        cy: \"80\",\n                                        r: normalizedRadius,\n                                        stroke: colors[index % colors.length],\n                                        strokeWidth: strokeWidth,\n                                        strokeDasharray: strokeDasharray,\n                                        strokeDashoffset: strokeDashoffset,\n                                        fill: \"transparent\",\n                                        strokeLinecap: \"round\",\n                                        className: \"transition-all duration-300 hover:opacity-80 cursor-pointer\",\n                                        style: {\n                                            filter: 'drop-shadow(0 2px 4px rgba(0,0,0,0.1))'\n                                        }\n                                    }, index, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                        lineNumber: 530,\n                                        columnNumber: 17\n                                    }, this);\n                                })\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                            lineNumber: 510,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute inset-0 flex items-center justify-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl font-bold text-gray-900\",\n                                        children: data.length\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                        lineNumber: 553,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm text-gray-500\",\n                                        children: \"Providers\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                        lineNumber: 554,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                lineNumber: 552,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                            lineNumber: 551,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                    lineNumber: 509,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-2 w-full\",\n                    children: data.slice(0, 4).map((item, index)=>{\n                        const percentage = (item.cost / total * 100).toFixed(1);\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between py-2 px-3 rounded-lg hover:bg-gray-50 transition-colors duration-150\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-3 h-3 rounded-full mr-3 shadow-sm\",\n                                            style: {\n                                                backgroundColor: colors[index]\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                            lineNumber: 566,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-sm font-medium text-gray-700 capitalize\",\n                                            children: item.name\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                            lineNumber: 570,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                    lineNumber: 565,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-right\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm font-semibold text-gray-900\",\n                                            children: [\n                                                percentage,\n                                                \"%\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                            lineNumber: 573,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-xs text-gray-500\",\n                                            children: formatCurrency(item.cost)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                            lineNumber: 574,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                    lineNumber: 572,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, index, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                            lineNumber: 564,\n                            columnNumber: 15\n                        }, this);\n                    })\n                }, void 0, false, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                    lineNumber: 560,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n            lineNumber: 508,\n            columnNumber: 7\n        }, this);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-[#faf8f5] p-6\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-7xl mx-auto space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4 mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-3xl font-bold text-gray-900 mb-2\",\n                                    children: \"Analytics Overview\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                    lineNumber: 590,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-600\",\n                                    children: \"Track your LLM usage, costs, and performance across all providers\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                    lineNumber: 591,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                            lineNumber: 589,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                    value: timeRange,\n                                    onChange: (e)=>setTimeRange(e.target.value),\n                                    className: \"px-4 py-2 bg-white border border-gray-200 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-[#ff6b35] focus:border-transparent\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"7\",\n                                            children: \"Last 7 days\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                            lineNumber: 602,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"30\",\n                                            children: \"Last 30 days\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                            lineNumber: 603,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"90\",\n                                            children: \"Last 90 days\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                            lineNumber: 604,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                    lineNumber: 597,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setShowFilters(!showFilters),\n                                    className: \"px-4 py-2 bg-[#ff6b35] text-white rounded-lg text-sm hover:bg-[#e55a2b] transition-colors duration-200\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowTrendingDownIcon_ArrowTrendingUpIcon_BellIcon_BoltIcon_CalendarIcon_ChartBarIcon_ChartPieIcon_CheckCircleIcon_CpuChipIcon_CurrencyDollarIcon_ExclamationTriangleIcon_FunnelIcon_LightBulbIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                            className: \"h-4 w-4 mr-2 inline\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                            lineNumber: 610,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"Filters\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                    lineNumber: 606,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                            lineNumber: 596,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                    lineNumber: 588,\n                    columnNumber: 9\n                }, this),\n                showFilters && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"card p-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-lg font-semibold text-gray-900 mb-4\",\n                            children: \"Advanced Filters\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                            lineNumber: 619,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                            children: \"Time Range\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                            lineNumber: 622,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                            value: timeRange,\n                                            onChange: (e)=>setTimeRange(e.target.value),\n                                            className: \"input-field\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"7\",\n                                                    children: \"Last 7 days\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                    lineNumber: 628,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"30\",\n                                                    children: \"Last 30 days\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                    lineNumber: 629,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"90\",\n                                                    children: \"Last 90 days\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                    lineNumber: 630,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"365\",\n                                                    children: \"Last year\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                    lineNumber: 631,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                            lineNumber: 623,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                    lineNumber: 621,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                            children: \"API Configuration\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                            lineNumber: 636,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                            value: selectedConfig,\n                                            onChange: (e)=>setSelectedConfig(e.target.value),\n                                            className: \"input-field\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"\",\n                                                    children: \"All Configurations\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                    lineNumber: 642,\n                                                    columnNumber: 17\n                                                }, this),\n                                                customConfigs.map((config)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: config.id,\n                                                        children: config.name\n                                                    }, config.id, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                        lineNumber: 644,\n                                                        columnNumber: 19\n                                                    }, this))\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                            lineNumber: 637,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                    lineNumber: 635,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                            children: \"Start Date\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                            lineNumber: 652,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"date\",\n                                            value: startDate,\n                                            onChange: (e)=>setStartDate(e.target.value),\n                                            className: \"input-field\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                            lineNumber: 653,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                    lineNumber: 651,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                            children: \"End Date\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                            lineNumber: 662,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"date\",\n                                            value: endDate,\n                                            onChange: (e)=>setEndDate(e.target.value),\n                                            className: \"input-field\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                            lineNumber: 663,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                    lineNumber: 661,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                            lineNumber: 620,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-4 flex space-x-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: fetchAnalyticsData,\n                                    className: \"btn-primary\",\n                                    children: \"Apply Filters\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                    lineNumber: 673,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: resetFilters,\n                                    className: \"btn-secondary\",\n                                    children: \"Reset Filters\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                    lineNumber: 676,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                            lineNumber: 672,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                    lineNumber: 618,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"lg:col-span-2 bg-white rounded-2xl p-6 shadow-sm border border-gray-100\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between mb-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-lg font-semibold text-gray-900\",\n                                                    children: \"Usage Analytics\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                    lineNumber: 691,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-500\",\n                                                    children: \"Cost trends over time\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                    lineNumber: 692,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                            lineNumber: 690,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-4 text-sm\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-3 h-3 bg-purple-500 rounded-full mr-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                            lineNumber: 696,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-gray-600\",\n                                                            children: \"Cost\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                            lineNumber: 697,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                    lineNumber: 695,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-3 h-3 bg-cyan-500 rounded-full mr-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                            lineNumber: 700,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-gray-600\",\n                                                            children: \"Requests\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                            lineNumber: 701,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                    lineNumber: 699,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                            lineNumber: 694,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                    lineNumber: 689,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ProfessionalLineChart, {\n                                    data: timeSeriesData\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                    lineNumber: 706,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-6 pt-6 border-t border-gray-100\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-2xl font-bold text-gray-900 mb-1\",\n                                                children: formatCurrency((summary === null || summary === void 0 ? void 0 : summary.total_cost) || 0)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                lineNumber: 710,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-sm text-gray-500\",\n                                                children: \"Total spend this period\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                lineNumber: 713,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                        lineNumber: 709,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                    lineNumber: 708,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                            lineNumber: 688,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white rounded-2xl p-6 shadow-sm border border-gray-100\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mb-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-semibold text-gray-900 mb-1\",\n                                            children: \"Cost Distribution\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                            lineNumber: 721,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-gray-500\",\n                                            children: \"By provider\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                            lineNumber: 722,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                    lineNumber: 720,\n                                    columnNumber: 13\n                                }, this),\n                                (analyticsData === null || analyticsData === void 0 ? void 0 : analyticsData.grouped_data.length) && summary ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ProfessionalDonutChart, {\n                                    data: analyticsData.grouped_data,\n                                    total: summary.total_cost\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                    lineNumber: 726,\n                                    columnNumber: 15\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center py-8\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowTrendingDownIcon_ArrowTrendingUpIcon_BellIcon_BoltIcon_CalendarIcon_ChartBarIcon_ChartPieIcon_CheckCircleIcon_CpuChipIcon_CurrencyDollarIcon_ExclamationTriangleIcon_FunnelIcon_LightBulbIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                            className: \"h-12 w-12 mx-auto mb-4 text-gray-300\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                            lineNumber: 732,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-500\",\n                                            children: \"No provider data available\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                            lineNumber: 733,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                    lineNumber: 731,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                            lineNumber: 719,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                    lineNumber: 686,\n                    columnNumber: 9\n                }, this),\n                summary && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white rounded-2xl p-6 shadow-sm border border-gray-100\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between mb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"p-2 bg-orange-50 rounded-lg\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowTrendingDownIcon_ArrowTrendingUpIcon_BellIcon_BoltIcon_CalendarIcon_ChartBarIcon_ChartPieIcon_CheckCircleIcon_CpuChipIcon_CurrencyDollarIcon_ExclamationTriangleIcon_FunnelIcon_LightBulbIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                className: \"h-5 w-5 text-[#ff6b35]\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                lineNumber: 746,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                            lineNumber: 745,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-right\",\n                                            children: costTrend && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center text-sm \".concat(costTrend.isPositive ? 'text-red-500' : 'text-green-500'),\n                                                children: [\n                                                    costTrend.isPositive ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowTrendingDownIcon_ArrowTrendingUpIcon_BellIcon_BoltIcon_CalendarIcon_ChartBarIcon_ChartPieIcon_CheckCircleIcon_CpuChipIcon_CurrencyDollarIcon_ExclamationTriangleIcon_FunnelIcon_LightBulbIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                        className: \"h-4 w-4 mr-1\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                        lineNumber: 752,\n                                                        columnNumber: 25\n                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowTrendingDownIcon_ArrowTrendingUpIcon_BellIcon_BoltIcon_CalendarIcon_ChartBarIcon_ChartPieIcon_CheckCircleIcon_CpuChipIcon_CurrencyDollarIcon_ExclamationTriangleIcon_FunnelIcon_LightBulbIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                        className: \"h-4 w-4 mr-1\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                        lineNumber: 754,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    costTrend.percentage.toFixed(1),\n                                                    \"%\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                lineNumber: 750,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                            lineNumber: 748,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                    lineNumber: 744,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-2xl font-bold text-gray-900\",\n                                            children: formatCurrency(summary.total_cost)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                            lineNumber: 762,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-gray-600\",\n                                            children: \"Total spend\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                            lineNumber: 765,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-gray-500\",\n                                            children: [\n                                                formatCurrency(summary.average_cost_per_request),\n                                                \" avg per request\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                            lineNumber: 766,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                    lineNumber: 761,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                            lineNumber: 743,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white rounded-2xl p-6 shadow-sm border border-gray-100\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between mb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"p-2 rounded-lg \".concat(summary.success_rate >= 95 ? 'bg-green-50' : 'bg-red-50'),\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowTrendingDownIcon_ArrowTrendingUpIcon_BellIcon_BoltIcon_CalendarIcon_ChartBarIcon_ChartPieIcon_CheckCircleIcon_CpuChipIcon_CurrencyDollarIcon_ExclamationTriangleIcon_FunnelIcon_LightBulbIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                className: \"h-5 w-5 \".concat(summary.success_rate >= 95 ? 'text-green-600' : 'text-red-600')\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                lineNumber: 776,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                            lineNumber: 775,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-right\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center text-sm \".concat(summary.success_rate >= 95 ? 'text-green-500' : 'text-red-500'),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowTrendingDownIcon_ArrowTrendingUpIcon_BellIcon_BoltIcon_CalendarIcon_ChartBarIcon_ChartPieIcon_CheckCircleIcon_CpuChipIcon_CurrencyDollarIcon_ExclamationTriangleIcon_FunnelIcon_LightBulbIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                        className: \"h-4 w-4 mr-1\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                        lineNumber: 780,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    summary.success_rate >= 95 ? 'Excellent' : 'Needs attention'\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                lineNumber: 779,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                            lineNumber: 778,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                    lineNumber: 774,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-2xl font-bold text-gray-900\",\n                                            children: [\n                                                summary.success_rate.toFixed(1),\n                                                \"%\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                            lineNumber: 786,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-gray-600\",\n                                            children: \"Success rate\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                            lineNumber: 789,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-gray-500\",\n                                            children: [\n                                                formatNumber(summary.successful_requests),\n                                                \" of \",\n                                                formatNumber(summary.total_requests),\n                                                \" requests\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                            lineNumber: 790,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                    lineNumber: 785,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                            lineNumber: 773,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white rounded-2xl p-6 shadow-sm border border-gray-100\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between mb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"p-2 bg-purple-50 rounded-lg\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowTrendingDownIcon_ArrowTrendingUpIcon_BellIcon_BoltIcon_CalendarIcon_ChartBarIcon_ChartPieIcon_CheckCircleIcon_CpuChipIcon_CurrencyDollarIcon_ExclamationTriangleIcon_FunnelIcon_LightBulbIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                className: \"h-5 w-5 text-purple-600\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                lineNumber: 800,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                            lineNumber: 799,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-right\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center text-sm text-purple-500\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowTrendingDownIcon_ArrowTrendingUpIcon_BellIcon_BoltIcon_CalendarIcon_ChartBarIcon_ChartPieIcon_CheckCircleIcon_CpuChipIcon_CurrencyDollarIcon_ExclamationTriangleIcon_FunnelIcon_LightBulbIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                        className: \"h-4 w-4 mr-1\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                        lineNumber: 804,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    \"Tokens\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                lineNumber: 803,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                            lineNumber: 802,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                    lineNumber: 798,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-2xl font-bold text-gray-900\",\n                                            children: formatNumber(summary.total_input_tokens + summary.total_output_tokens)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                            lineNumber: 810,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-gray-600\",\n                                            children: \"Total tokens\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                            lineNumber: 813,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-gray-500\",\n                                            children: [\n                                                formatNumber(summary.total_input_tokens),\n                                                \" input • \",\n                                                formatNumber(summary.total_output_tokens),\n                                                \" output\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                            lineNumber: 814,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                    lineNumber: 809,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                            lineNumber: 797,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white rounded-2xl p-6 shadow-sm border border-gray-100\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between mb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"p-2 bg-indigo-50 rounded-lg\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowTrendingDownIcon_ArrowTrendingUpIcon_BellIcon_BoltIcon_CalendarIcon_ChartBarIcon_ChartPieIcon_CheckCircleIcon_CpuChipIcon_CurrencyDollarIcon_ExclamationTriangleIcon_FunnelIcon_LightBulbIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                className: \"h-5 w-5 text-indigo-600\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                lineNumber: 824,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                            lineNumber: 823,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-right\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center text-sm \".concat(projectedMonthlyCost > monthlyBudget ? 'text-red-500' : 'text-green-500'),\n                                                children: [\n                                                    projectedMonthlyCost > monthlyBudget ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowTrendingDownIcon_ArrowTrendingUpIcon_BellIcon_BoltIcon_CalendarIcon_ChartBarIcon_ChartPieIcon_CheckCircleIcon_CpuChipIcon_CurrencyDollarIcon_ExclamationTriangleIcon_FunnelIcon_LightBulbIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                        className: \"h-4 w-4 mr-1\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                        lineNumber: 829,\n                                                        columnNumber: 23\n                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowTrendingDownIcon_ArrowTrendingUpIcon_BellIcon_BoltIcon_CalendarIcon_ChartBarIcon_ChartPieIcon_CheckCircleIcon_CpuChipIcon_CurrencyDollarIcon_ExclamationTriangleIcon_FunnelIcon_LightBulbIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                        className: \"h-4 w-4 mr-1\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                        lineNumber: 831,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    projectedMonthlyCost > monthlyBudget ? 'Over budget' : 'On track'\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                lineNumber: 827,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                            lineNumber: 826,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                    lineNumber: 822,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-2xl font-bold text-gray-900\",\n                                            children: formatCurrency(projectedMonthlyCost)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                            lineNumber: 838,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-gray-600\",\n                                            children: \"Projected monthly\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                            lineNumber: 841,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-gray-500\",\n                                            children: [\n                                                \"Budget: \",\n                                                formatCurrency(monthlyBudget)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                            lineNumber: 842,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                    lineNumber: 837,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                            lineNumber: 821,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                    lineNumber: 741,\n                    columnNumber: 11\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white rounded-2xl p-6 shadow-sm border border-gray-100\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mb-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-semibold text-gray-900 mb-1\",\n                                            children: \"Provider Performance\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                            lineNumber: 855,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-gray-500\",\n                                            children: \"Detailed breakdown by provider\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                            lineNumber: 856,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                    lineNumber: 854,\n                                    columnNumber: 13\n                                }, this),\n                                (analyticsData === null || analyticsData === void 0 ? void 0 : analyticsData.grouped_data.length) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"overflow-hidden\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                                        className: \"w-full\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                    className: \"border-b border-gray-100\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            className: \"text-left py-3 text-sm font-medium text-gray-600\",\n                                                            children: \"Provider\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                            lineNumber: 863,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            className: \"text-right py-3 text-sm font-medium text-gray-600\",\n                                                            children: \"Cost\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                            lineNumber: 864,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            className: \"text-right py-3 text-sm font-medium text-gray-600\",\n                                                            children: \"Requests\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                            lineNumber: 865,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            className: \"text-right py-3 text-sm font-medium text-gray-600\",\n                                                            children: \"Share\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                            lineNumber: 866,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                    lineNumber: 862,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                lineNumber: 861,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                                className: \"divide-y divide-gray-50\",\n                                                children: analyticsData.grouped_data.sort((a, b)=>b.cost - a.cost).map((provider, index)=>{\n                                                    const colors = [\n                                                        '#ff6b35',\n                                                        '#3b82f6',\n                                                        '#10b981',\n                                                        '#f59e0b',\n                                                        '#ef4444',\n                                                        '#8b5cf6'\n                                                    ];\n                                                    const percentage = (provider.cost / ((summary === null || summary === void 0 ? void 0 : summary.total_cost) || 1) * 100).toFixed(1);\n                                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                        className: \"hover:bg-gray-50 transition-colors duration-150\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                className: \"py-4\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"w-3 h-3 rounded-full mr-3\",\n                                                                            style: {\n                                                                                backgroundColor: colors[index % colors.length]\n                                                                            }\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                            lineNumber: 879,\n                                                                            columnNumber: 33\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"font-medium text-gray-900 capitalize\",\n                                                                            children: provider.name\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                            lineNumber: 883,\n                                                                            columnNumber: 33\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                    lineNumber: 878,\n                                                                    columnNumber: 31\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                lineNumber: 877,\n                                                                columnNumber: 29\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                className: \"py-4 text-right font-semibold text-gray-900\",\n                                                                children: formatCurrency(provider.cost)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                lineNumber: 886,\n                                                                columnNumber: 29\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                className: \"py-4 text-right text-gray-600\",\n                                                                children: formatNumber(provider.requests)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                lineNumber: 889,\n                                                                columnNumber: 29\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                className: \"py-4 text-right\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800\",\n                                                                    children: [\n                                                                        percentage,\n                                                                        \"%\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                    lineNumber: 893,\n                                                                    columnNumber: 31\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                lineNumber: 892,\n                                                                columnNumber: 29\n                                                            }, this)\n                                                        ]\n                                                    }, provider.name, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                        lineNumber: 876,\n                                                        columnNumber: 27\n                                                    }, this);\n                                                })\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                lineNumber: 869,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                        lineNumber: 860,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                    lineNumber: 859,\n                                    columnNumber: 15\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center py-8 text-gray-500\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowTrendingDownIcon_ArrowTrendingUpIcon_BellIcon_BoltIcon_CalendarIcon_ChartBarIcon_ChartPieIcon_CheckCircleIcon_CpuChipIcon_CurrencyDollarIcon_ExclamationTriangleIcon_FunnelIcon_LightBulbIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                            className: \"h-12 w-12 mx-auto mb-4 text-gray-300\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                            lineNumber: 905,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            children: \"No provider data available\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                            lineNumber: 906,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                    lineNumber: 904,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                            lineNumber: 853,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white rounded-2xl p-6 shadow-sm border border-gray-100\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mb-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-semibold text-gray-900 mb-1\",\n                                            children: \"Top Models\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                            lineNumber: 914,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-gray-500\",\n                                            children: \"Most expensive models by cost\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                            lineNumber: 915,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                    lineNumber: 913,\n                                    columnNumber: 13\n                                }, this),\n                                (modelAnalytics === null || modelAnalytics === void 0 ? void 0 : modelAnalytics.grouped_data.length) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"overflow-hidden\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                                        className: \"w-full\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                    className: \"border-b border-gray-100\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            className: \"text-left py-3 text-sm font-medium text-gray-600\",\n                                                            children: \"Model\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                            lineNumber: 923,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            className: \"text-right py-3 text-sm font-medium text-gray-600\",\n                                                            children: \"Cost\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                            lineNumber: 924,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            className: \"text-right py-3 text-sm font-medium text-gray-600\",\n                                                            children: \"Requests\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                            lineNumber: 925,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            className: \"text-right py-3 text-sm font-medium text-gray-600\",\n                                                            children: \"Avg/Request\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                            lineNumber: 926,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                    lineNumber: 922,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                lineNumber: 921,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                                className: \"divide-y divide-gray-50\",\n                                                children: modelAnalytics.grouped_data.sort((a, b)=>b.cost - a.cost).slice(0, 5).map((model, index)=>{\n                                                    const colors = [\n                                                        '#ff6b35',\n                                                        '#3b82f6',\n                                                        '#10b981',\n                                                        '#f59e0b',\n                                                        '#ef4444'\n                                                    ];\n                                                    const avgCost = model.cost / Math.max(model.requests, 1);\n                                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                        className: \"hover:bg-gray-50 transition-colors duration-150\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                className: \"py-4\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"w-3 h-3 rounded-full mr-3\",\n                                                                            style: {\n                                                                                backgroundColor: colors[index % colors.length]\n                                                                            }\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                            lineNumber: 940,\n                                                                            columnNumber: 33\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"font-medium text-gray-900\",\n                                                                                    children: model.name.length > 20 ? model.name.substring(0, 20) + '...' : model.name\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                                    lineNumber: 945,\n                                                                                    columnNumber: 35\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"text-xs text-gray-500 mt-1\",\n                                                                                    children: [\n                                                                                        formatNumber(model.input_tokens + model.output_tokens),\n                                                                                        \" tokens\"\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                                    lineNumber: 946,\n                                                                                    columnNumber: 35\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                            lineNumber: 944,\n                                                                            columnNumber: 33\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                    lineNumber: 939,\n                                                                    columnNumber: 31\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                lineNumber: 938,\n                                                                columnNumber: 29\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                className: \"py-4 text-right font-semibold text-gray-900\",\n                                                                children: formatCurrency(model.cost)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                lineNumber: 952,\n                                                                columnNumber: 29\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                className: \"py-4 text-right text-gray-600\",\n                                                                children: formatNumber(model.requests)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                lineNumber: 955,\n                                                                columnNumber: 29\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                className: \"py-4 text-right text-gray-600\",\n                                                                children: formatCurrency(avgCost)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                lineNumber: 958,\n                                                                columnNumber: 29\n                                                            }, this)\n                                                        ]\n                                                    }, model.name, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                        lineNumber: 937,\n                                                        columnNumber: 27\n                                                    }, this);\n                                                })\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                lineNumber: 929,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                        lineNumber: 920,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                    lineNumber: 919,\n                                    columnNumber: 15\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center py-8 text-gray-500\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowTrendingDownIcon_ArrowTrendingUpIcon_BellIcon_BoltIcon_CalendarIcon_ChartBarIcon_ChartPieIcon_CheckCircleIcon_CpuChipIcon_CurrencyDollarIcon_ExclamationTriangleIcon_FunnelIcon_LightBulbIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                            className: \"h-12 w-12 mx-auto mb-4 text-gray-300\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                            lineNumber: 969,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            children: \"No model data available\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                            lineNumber: 970,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                    lineNumber: 968,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                            lineNumber: 912,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                    lineNumber: 851,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"card p-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-xl font-semibold text-gray-900 mb-6 flex items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowTrendingDownIcon_ArrowTrendingUpIcon_BellIcon_BoltIcon_CalendarIcon_ChartBarIcon_ChartPieIcon_CheckCircleIcon_CpuChipIcon_CurrencyDollarIcon_ExclamationTriangleIcon_FunnelIcon_LightBulbIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                    className: \"h-5 w-5 mr-2 text-green-600\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                    lineNumber: 979,\n                                    columnNumber: 11\n                                }, this),\n                                \"Performance Insights\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                            lineNumber: 978,\n                            columnNumber: 9\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-3 gap-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center p-4 rounded-lg bg-green-50\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-2xl font-bold text-green-600 mb-2\",\n                                            children: summary ? formatNumber(summary.total_input_tokens + summary.total_output_tokens) : 0\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                            lineNumber: 984,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm text-gray-600\",\n                                            children: \"Total Tokens Processed\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                            lineNumber: 987,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-xs text-gray-500 mt-1\",\n                                            children: summary ? \"\".concat(formatNumber(summary.total_input_tokens), \" in • \").concat(formatNumber(summary.total_output_tokens), \" out\") : ''\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                            lineNumber: 988,\n                                            columnNumber: 13\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                    lineNumber: 983,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center p-4 rounded-lg bg-blue-50\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-2xl font-bold text-blue-600 mb-2\",\n                                            children: summary ? formatNumber(summary.total_requests) : 0\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                            lineNumber: 994,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm text-gray-600\",\n                                            children: \"Total API Requests\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                            lineNumber: 997,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-xs text-gray-500 mt-1\",\n                                            children: summary ? \"\".concat((summary.total_requests / parseInt(timeRange)).toFixed(1), \" per day avg\") : ''\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                            lineNumber: 998,\n                                            columnNumber: 13\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                    lineNumber: 993,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center p-4 rounded-lg bg-purple-50\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-2xl font-bold text-purple-600 mb-2\",\n                                            children: summary ? formatCurrency(summary.average_cost_per_request) : '$0.00'\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                            lineNumber: 1004,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm text-gray-600\",\n                                            children: \"Average Cost per Request\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                            lineNumber: 1007,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-xs text-gray-500 mt-1\",\n                                            children: \"Across all providers\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                            lineNumber: 1008,\n                                            columnNumber: 13\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                    lineNumber: 1003,\n                                    columnNumber: 11\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                            lineNumber: 982,\n                            columnNumber: 9\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                    lineNumber: 977,\n                    columnNumber: 7\n                }, this),\n                (costAlerts.length > 0 || recommendations.length > 0) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8\",\n                    children: [\n                        costAlerts.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white rounded-2xl p-6 shadow-sm border border-gray-100\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mb-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-semibold text-gray-900 mb-1 flex items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowTrendingDownIcon_ArrowTrendingUpIcon_BellIcon_BoltIcon_CalendarIcon_ChartBarIcon_ChartPieIcon_CheckCircleIcon_CpuChipIcon_CurrencyDollarIcon_ExclamationTriangleIcon_FunnelIcon_LightBulbIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                    className: \"h-5 w-5 mr-2 text-[#ff6b35]\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                    lineNumber: 1023,\n                                                    columnNumber: 21\n                                                }, this),\n                                                \"Alerts & Warnings\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                            lineNumber: 1022,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-gray-500\",\n                                            children: \"Important notifications about your usage\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                            lineNumber: 1026,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                    lineNumber: 1021,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-4\",\n                                    children: costAlerts.map((alert, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"p-4 rounded-xl border-l-4 \".concat(alert.type === 'danger' ? 'border-red-500 bg-red-50' : alert.type === 'warning' ? 'border-yellow-500 bg-yellow-50' : 'border-blue-500 bg-blue-50'),\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-start\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowTrendingDownIcon_ArrowTrendingUpIcon_BellIcon_BoltIcon_CalendarIcon_ChartBarIcon_ChartPieIcon_CheckCircleIcon_CpuChipIcon_CurrencyDollarIcon_ExclamationTriangleIcon_FunnelIcon_LightBulbIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                        className: \"h-5 w-5 mr-3 mt-0.5 \".concat(alert.type === 'danger' ? 'text-red-600' : alert.type === 'warning' ? 'text-yellow-600' : 'text-blue-600')\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                        lineNumber: 1039,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                className: \"font-medium text-gray-900\",\n                                                                children: alert.title\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                lineNumber: 1045,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-gray-600 mt-1\",\n                                                                children: alert.message\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                lineNumber: 1046,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            alert.value && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-lg font-bold mt-2 \".concat(alert.type === 'danger' ? 'text-red-600' : alert.type === 'warning' ? 'text-yellow-600' : 'text-blue-600'),\n                                                                children: alert.value\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                lineNumber: 1048,\n                                                                columnNumber: 29\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                        lineNumber: 1044,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                lineNumber: 1038,\n                                                columnNumber: 23\n                                            }, this)\n                                        }, index, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                            lineNumber: 1030,\n                                            columnNumber: 21\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                    lineNumber: 1028,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                            lineNumber: 1020,\n                            columnNumber: 15\n                        }, this),\n                        recommendations.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white rounded-2xl p-6 shadow-sm border border-gray-100\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mb-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-semibold text-gray-900 mb-1 flex items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowTrendingDownIcon_ArrowTrendingUpIcon_BellIcon_BoltIcon_CalendarIcon_ChartBarIcon_ChartPieIcon_CheckCircleIcon_CpuChipIcon_CurrencyDollarIcon_ExclamationTriangleIcon_FunnelIcon_LightBulbIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                    className: \"h-5 w-5 mr-2 text-yellow-500\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                    lineNumber: 1069,\n                                                    columnNumber: 21\n                                                }, this),\n                                                \"Smart Recommendations\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                            lineNumber: 1068,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-gray-500\",\n                                            children: \"AI-powered optimization suggestions\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                            lineNumber: 1072,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                    lineNumber: 1067,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-4\",\n                                    children: recommendations.map((rec, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"p-4 rounded-xl bg-gradient-to-r from-blue-50 to-purple-50 border border-blue-100\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-start\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"p-2 rounded-lg bg-white shadow-sm mr-4\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(rec.icon, {\n                                                            className: \"h-4 w-4 text-blue-600\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                            lineNumber: 1079,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                        lineNumber: 1078,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                className: \"font-medium text-gray-900\",\n                                                                children: rec.title\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                lineNumber: 1082,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-gray-600 mt-1\",\n                                                                children: rec.description\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                lineNumber: 1083,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            rec.potential_savings && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"mt-2 inline-flex items-center px-3 py-1 rounded-full text-xs bg-green-100 text-green-800\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowTrendingDownIcon_ArrowTrendingUpIcon_BellIcon_BoltIcon_CalendarIcon_ChartBarIcon_ChartPieIcon_CheckCircleIcon_CpuChipIcon_CurrencyDollarIcon_ExclamationTriangleIcon_FunnelIcon_LightBulbIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                        className: \"h-3 w-3 mr-1\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                        lineNumber: 1086,\n                                                                        columnNumber: 31\n                                                                    }, this),\n                                                                    \"Save \",\n                                                                    formatCurrency(rec.potential_savings)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                                lineNumber: 1085,\n                                                                columnNumber: 29\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                        lineNumber: 1081,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                lineNumber: 1077,\n                                                columnNumber: 23\n                                            }, this)\n                                        }, index, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                            lineNumber: 1076,\n                                            columnNumber: 21\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                    lineNumber: 1074,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                            lineNumber: 1066,\n                            columnNumber: 15\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                    lineNumber: 1017,\n                    columnNumber: 11\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-2xl p-6 shadow-sm border border-gray-100 mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between mb-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-semibold text-gray-900 mb-1 flex items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowTrendingDownIcon_ArrowTrendingUpIcon_BellIcon_BoltIcon_CalendarIcon_ChartBarIcon_ChartPieIcon_CheckCircleIcon_CpuChipIcon_CurrencyDollarIcon_ExclamationTriangleIcon_FunnelIcon_LightBulbIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                    className: \"h-5 w-5 mr-2 text-green-600\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                    lineNumber: 1105,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"Budget Management\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                            lineNumber: 1104,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-gray-500\",\n                                            children: \"Monitor and control your spending\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                            lineNumber: 1108,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                    lineNumber: 1103,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setShowBudgetSettings(!showBudgetSettings),\n                                    className: \"px-4 py-2 bg-[#ff6b35] text-white rounded-lg text-sm hover:bg-[#e55a2b] transition-colors duration-200\",\n                                    children: showBudgetSettings ? 'Hide Settings' : 'Configure'\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                    lineNumber: 1110,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                            lineNumber: 1102,\n                            columnNumber: 11\n                        }, this),\n                        showBudgetSettings && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-6 p-4 bg-gray-50 rounded-xl\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"max-w-xs\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                            children: \"Monthly Budget (USD)\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                            lineNumber: 1121,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"number\",\n                                            value: monthlyBudget,\n                                            onChange: (e)=>setMonthlyBudget(parseFloat(e.target.value) || 0),\n                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#ff6b35] focus:border-transparent\",\n                                            placeholder: \"100.00\",\n                                            step: \"0.01\",\n                                            min: \"0\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                            lineNumber: 1124,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                    lineNumber: 1120,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-3 text-xs text-gray-600\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            children: \"• Alerts trigger at 80% (warning) and 100% (danger) of budget\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                            lineNumber: 1135,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            children: [\n                                                \"• Current projection: \",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                    children: formatCurrency(projectedMonthlyCost)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                                    lineNumber: 1136,\n                                                    columnNumber: 42\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                            lineNumber: 1136,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                    lineNumber: 1134,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                            lineNumber: 1119,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center p-4 rounded-xl bg-blue-50\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-2xl font-bold text-blue-600\",\n                                            children: formatCurrency(monthlyBudget)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                            lineNumber: 1143,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm text-gray-600\",\n                                            children: \"Monthly Budget\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                            lineNumber: 1146,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                    lineNumber: 1142,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center p-4 rounded-xl bg-orange-50\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-2xl font-bold text-[#ff6b35]\",\n                                            children: formatCurrency(projectedMonthlyCost)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                            lineNumber: 1150,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm text-gray-600\",\n                                            children: \"Projected Spend\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                            lineNumber: 1153,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                    lineNumber: 1149,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center p-4 rounded-xl bg-green-50\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-2xl font-bold text-green-600\",\n                                            children: formatCurrency(Math.max(0, monthlyBudget - projectedMonthlyCost))\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                            lineNumber: 1157,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm text-gray-600\",\n                                            children: projectedMonthlyCost > monthlyBudget ? 'Over Budget' : 'Remaining'\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                            lineNumber: 1160,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                    lineNumber: 1156,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                            lineNumber: 1141,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                    lineNumber: 1101,\n                    columnNumber: 9\n                }, this),\n                previousSummary && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-2xl p-6 shadow-sm border border-gray-100\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-semibold text-gray-900 mb-1 flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowTrendingDownIcon_ArrowTrendingUpIcon_BellIcon_BoltIcon_CalendarIcon_ChartBarIcon_ChartPieIcon_CheckCircleIcon_CpuChipIcon_CurrencyDollarIcon_ExclamationTriangleIcon_FunnelIcon_LightBulbIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                            className: \"h-5 w-5 mr-2 text-purple-600\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                            lineNumber: 1172,\n                                            columnNumber: 17\n                                        }, this),\n                                        \"Period Comparison\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                    lineNumber: 1171,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-gray-500\",\n                                    children: \"Performance vs previous period\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                    lineNumber: 1175,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                            lineNumber: 1170,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-3 gap-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center p-4 rounded-xl bg-gray-50\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm font-medium text-gray-600 mb-2\",\n                                            children: \"Cost Change\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                            lineNumber: 1179,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-2xl font-bold \".concat((costTrend === null || costTrend === void 0 ? void 0 : costTrend.isPositive) ? 'text-red-600' : 'text-green-600'),\n                                            children: [\n                                                (costTrend === null || costTrend === void 0 ? void 0 : costTrend.isPositive) ? '+' : '-',\n                                                costTrend === null || costTrend === void 0 ? void 0 : costTrend.percentage.toFixed(1),\n                                                \"%\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                            lineNumber: 1180,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-xs text-gray-500 mt-2\",\n                                            children: [\n                                                formatCurrency(previousSummary.total_cost),\n                                                \" → \",\n                                                formatCurrency((summary === null || summary === void 0 ? void 0 : summary.total_cost) || 0)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                            lineNumber: 1183,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                    lineNumber: 1178,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center p-4 rounded-xl bg-gray-50\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm font-medium text-gray-600 mb-2\",\n                                            children: \"Request Volume\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                            lineNumber: 1189,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-2xl font-bold \".concat((requestTrend === null || requestTrend === void 0 ? void 0 : requestTrend.isPositive) ? 'text-green-600' : 'text-red-600'),\n                                            children: [\n                                                (requestTrend === null || requestTrend === void 0 ? void 0 : requestTrend.isPositive) ? '+' : '-',\n                                                requestTrend === null || requestTrend === void 0 ? void 0 : requestTrend.percentage.toFixed(1),\n                                                \"%\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                            lineNumber: 1190,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-xs text-gray-500 mt-2\",\n                                            children: [\n                                                formatNumber(previousSummary.total_requests),\n                                                \" → \",\n                                                formatNumber((summary === null || summary === void 0 ? void 0 : summary.total_requests) || 0)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                            lineNumber: 1193,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                    lineNumber: 1188,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center p-4 rounded-xl bg-gray-50\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm font-medium text-gray-600 mb-2\",\n                                            children: \"Efficiency\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                            lineNumber: 1199,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-2xl font-bold \".concat(((summary === null || summary === void 0 ? void 0 : summary.average_cost_per_request) || 0) < previousSummary.average_cost_per_request ? 'text-green-600' : 'text-red-600'),\n                                            children: ((summary === null || summary === void 0 ? void 0 : summary.average_cost_per_request) || 0) < previousSummary.average_cost_per_request ? '↑' : '↓'\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                            lineNumber: 1200,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-xs text-gray-500 mt-2\",\n                                            children: [\n                                                formatCurrency(previousSummary.average_cost_per_request),\n                                                \" → \",\n                                                formatCurrency((summary === null || summary === void 0 ? void 0 : summary.average_cost_per_request) || 0)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                            lineNumber: 1205,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                    lineNumber: 1198,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                            lineNumber: 1177,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                    lineNumber: 1169,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n            lineNumber: 586,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n        lineNumber: 585,\n        columnNumber: 5\n    }, this);\n}\n_s(AnalyticsPageContent, \"qgClNvt3fYHqAHTRr8MfhVCyyr4=\");\n_c = AnalyticsPageContent;\nfunction AnalyticsPage() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react__WEBPACK_IMPORTED_MODULE_1__.Suspense, {\n        fallback: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"animate-pulse\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"h-8 bg-gray-200 rounded w-1/3 mb-4\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                            lineNumber: 1222,\n                            columnNumber: 11\n                        }, void 0),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"h-4 bg-gray-200 rounded w-1/2\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                            lineNumber: 1223,\n                            columnNumber: 11\n                        }, void 0)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                    lineNumber: 1221,\n                    columnNumber: 9\n                }, void 0),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\",\n                    children: [\n                        ...Array(4)\n                    ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"card p-6 animate-pulse\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"h-4 bg-gray-200 rounded w-1/2 mb-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                    lineNumber: 1228,\n                                    columnNumber: 15\n                                }, void 0),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"h-8 bg-gray-200 rounded w-3/4 mb-2\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                    lineNumber: 1229,\n                                    columnNumber: 15\n                                }, void 0),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"h-3 bg-gray-200 rounded w-1/3\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                                    lineNumber: 1230,\n                                    columnNumber: 15\n                                }, void 0)\n                            ]\n                        }, i, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                            lineNumber: 1227,\n                            columnNumber: 13\n                        }, void 0))\n                }, void 0, false, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n                    lineNumber: 1225,\n                    columnNumber: 9\n                }, void 0)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n            lineNumber: 1220,\n            columnNumber: 7\n        }, void 0),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AnalyticsPageContent, {}, void 0, false, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n            lineNumber: 1236,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\",\n        lineNumber: 1219,\n        columnNumber: 5\n    }, this);\n}\n_c1 = AnalyticsPage;\nvar _c, _c1;\n$RefreshReg$(_c, \"AnalyticsPageContent\");\n$RefreshReg$(_c1, \"AnalyticsPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/analytics/page.tsx\n"));

/***/ })

},
/******/ __webpack_require__ => { // webpackRuntimeModules
/******/ var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
/******/ __webpack_require__.O(0, ["main-app"], () => (__webpack_exec__("(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CRoKey%20App%5C%5Crokey-app%5C%5Csrc%5C%5Capp%5C%5Canalytics%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!")));
/******/ var __webpack_exports__ = __webpack_require__.O();
/******/ _N_E = __webpack_exports__;
/******/ }
]);