"use client";import{useFocusRing as y}from"@react-aria/focus";import{useHover as b}from"@react-aria/interactions";import{useMemo as P}from"react";import{useActivePress as B}from'../../hooks/use-active-press.js';import{useDisabled as c}from'../../internal/disabled.js';import{forwardRefWithAs as A,mergeProps as g,useRender as _}from'../../utils/render.js';let R="button";function v(a,u){var p;let l=c(),{disabled:e=l||!1,autoFocus:t=!1,...o}=a,{isFocusVisible:r,focusProps:i}=y({autoFocus:t}),{isHovered:s,hoverProps:T}=b({isDisabled:e}),{pressed:n,pressProps:d}=B({disabled:e}),f=g({ref:u,type:(p=o.type)!=null?p:"button",disabled:e||void 0,autoFocus:t},i,T,d),m=P(()=>({disabled:e,hover:s,focus:r,active:n,autofocus:t}),[e,s,r,n,t]);return _()({ourProps:f,theirProps:o,slot:m,defaultTag:R,name:"Button"})}let H=A(v);export{H as Button};
