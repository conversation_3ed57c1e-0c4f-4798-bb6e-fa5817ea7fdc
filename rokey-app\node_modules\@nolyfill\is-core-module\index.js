"use strict";const e=require("module"),s=new Set(["assert/strict","node:assert/strict","diagnostics_channel","node:diagnostics_channel","dns/promises","node:dns/promises","fs/promises","node:fs/promises","inspector/promises","node:inspector/promises","path/posix","node:path/posix","path/win32","node:path/win32","readline/promises","node:readline/promises","node:sea","stream/consumers","node:stream/consumers","stream/promises","node:stream/promises","stream/web","node:stream/web","node:test/reporters","test/mock_loader","node:test/mock_loader","node:test","timers/promises","node:timers/promises","util/types","node:util/types","wasi","node:wasi"].concat(e.builtinModules,e.builtinModules.map(e=>"node:"+e)));module.exports=(e,o)=>s.has(e);