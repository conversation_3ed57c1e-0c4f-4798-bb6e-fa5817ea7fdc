"use strict";
// THIS CODE WAS AUTOMATICALLY GENERATED
// DO NOT EDIT THIS CODE BY HAND
// RUN THE FOLLOWING COMMAND FROM THE WORKSPACE ROOT TO REGENERATE:
// npx nx generate-lib repo
Object.defineProperty(exports, "__esModule", { value: true });
exports.esnext_promise = void 0;
const base_config_1 = require("./base-config");
exports.esnext_promise = {
    libs: [],
    variables: [['PromiseConstructor', base_config_1.TYPE]],
};
