import { type NextRequest, NextResponse } from 'next/server';
import { createSupabaseServerClientFromRequest } from '@/lib/supabase/server';

interface RouteParams {
  params: Promise<{
    apiKeyId: string;
    roleName: string; // role_name (id) comes from the path
  }>;
}

// DELETE /api/keys/:apiKeyId/roles/:roleName
// Unassigns a role from an API key.
export async function DELETE(request: NextRequest, { params }: RouteParams) {
  const supabase = createSupabaseServerClientFromRequest(request);
  const { apiKeyId, roleName } = await params;

  // Get authenticated user from session
  const { data: { session }, error: sessionError } = await supabase.auth.getSession();
  if (sessionError || !session?.user) {
    console.error('Authentication error in DELETE role assignment:', sessionError);
    return NextResponse.json({ error: 'Unauthorized: You must be logged in to unassign roles.' }, { status: 401 });
  }

  const authenticatedUserId = session.user.id;

  if (!apiKeyId || !roleName) {
    return NextResponse.json({ error: 'API Key ID and Role Name are required' }, { status: 400 });
  }

  try {
    // Fetch API key details and the user_id of the custom_api_config owner for authorization
    const { data: apiKeyRecord, error: apiKeyFetchError } = await supabase
      .from('api_keys')
      .select(`
        custom_api_config_id,
        custom_api_configs ( user_id )
      `)
      .eq('id', apiKeyId)
      .single();

    if (apiKeyFetchError || !apiKeyRecord) {
      console.error('API Key not found for authorization check:', apiKeyFetchError);
      return NextResponse.json({ error: 'API Key not found or failed to fetch its details for authorization' }, { status: 404 });
    }

    const configOwnerUserId = (apiKeyRecord.custom_api_configs as unknown as { user_id: string })?.user_id;

    if (!configOwnerUserId) {
        // TEMPORARY: Allow placeholder user to proceed if config owner cannot be determined
        if (authenticatedUserId !== '00000000-0000-0000-0000-000000000000') {
            console.error('Could not determine the owner of the Custom API Configuration for the API Key during unassignment.');
            return NextResponse.json({ error: 'Could not determine the config owner for the API Key.' }, { status: 500 });
        } else {
            console.warn('Proceeding with unassignment for placeholder user despite not finding configOwnerUserId. This should be reviewed for production.');
        }
    }

    // Authorization check
    // TEMPORARY: If configOwnerUserId was not found, but it's the placeholder user, this check is effectively bypassed.
    if (configOwnerUserId && authenticatedUserId !== configOwnerUserId) {
        console.warn(`User ${authenticatedUserId} attempted to unassign role from API key ${apiKeyId} owned by user ${configOwnerUserId}.`);
        return NextResponse.json({ error: 'Forbidden. You do not own the configuration this API key belongs to.' }, { status: 403 });
    }

    // Proceed with unassignment
    const { error, count } = await supabase
      .from('api_key_role_assignments')
      .delete({ count: 'exact' })
      .eq('api_key_id', apiKeyId)
      .eq('role_name', roleName);

    if (error) {
      console.error('Supabase error unassigning role:', error);
      return NextResponse.json({ error: 'Failed to unassign role from API key', details: error.message }, { status: 500 });
    }

    if (count === 0) {
        return NextResponse.json({ error: 'Role assignment not found for this API key or already unassigned.' }, { status: 404 });
    }

    return NextResponse.json({ message: `Role '${roleName}' unassigned successfully from API key ${apiKeyId}` }, { status: 200 });
  } catch (e: any) {
    console.error('Error in DELETE /api/keys/:apiKeyId/roles/:roleName:', e);
    return NextResponse.json({ error: 'An unexpected error occurred', details: e.message }, { status: 500 });
  }
} 