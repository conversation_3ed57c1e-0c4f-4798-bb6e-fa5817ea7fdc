export default {
  "hljs": {
    "display": "block",
    "overflowX": "auto",
    "padding": "0.5em",
    "background": "#fff",
    "color": "#000"
  },
  "hljs-subst": {
    "color": "#000"
  },
  "hljs-string": {
    "color": "#756bb1"
  },
  "hljs-meta": {
    "color": "#756bb1"
  },
  "hljs-symbol": {
    "color": "#756bb1"
  },
  "hljs-template-tag": {
    "color": "#756bb1"
  },
  "hljs-template-variable": {
    "color": "#756bb1"
  },
  "hljs-addition": {
    "color": "#756bb1"
  },
  "hljs-comment": {
    "color": "#636363"
  },
  "hljs-quote": {
    "color": "#636363"
  },
  "hljs-number": {
    "color": "#31a354"
  },
  "hljs-regexp": {
    "color": "#31a354"
  },
  "hljs-literal": {
    "color": "#31a354"
  },
  "hljs-bullet": {
    "color": "#31a354"
  },
  "hljs-link": {
    "color": "#31a354"
  },
  "hljs-deletion": {
    "color": "#88f"
  },
  "hljs-variable": {
    "color": "#88f"
  },
  "hljs-keyword": {
    "color": "#3182bd"
  },
  "hljs-selector-tag": {
    "color": "#3182bd"
  },
  "hljs-title": {
    "color": "#3182bd"
  },
  "hljs-section": {
    "color": "#3182bd"
  },
  "hljs-built_in": {
    "color": "#3182bd"
  },
  "hljs-doctag": {
    "color": "#3182bd"
  },
  "hljs-type": {
    "color": "#3182bd"
  },
  "hljs-tag": {
    "color": "#3182bd"
  },
  "hljs-name": {
    "color": "#3182bd"
  },
  "hljs-selector-id": {
    "color": "#3182bd"
  },
  "hljs-selector-class": {
    "color": "#3182bd"
  },
  "hljs-strong": {
    "color": "#3182bd"
  },
  "hljs-emphasis": {
    "fontStyle": "italic"
  },
  "hljs-attribute": {
    "color": "#e6550d"
  }
};